package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.model.EirActivityZone;
import com.maersk.sd1.common.model.EstimateEmr;
import com.maersk.sd1.common.model.EstimateEmrDetail;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.dto.EliminarEstimadoEmrInput;
import com.maersk.sd1.sde.dto.EliminarEstimadoEmrOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

@Service
@RequiredArgsConstructor
public class EliminarEstimadoEmrService {

    private static final Logger logger = LogManager.getLogger(EliminarEstimadoEmrService.class);

    private final EstimateEmrRepository estimateEmrRepository;
    private final EstimateEmrDetailRepository estimateEmrDetailRepository;
    private final EirRepository eirRepository;
    private final EirActivityZoneRepository eirActivityZoneRepository;
    private final CatalogRepository catalogRepository;
    private final MessageLanguageRepository messageLanguageRepository;
    private final UserRepository userRepository;
    private final JdbcTemplate jdbcTemplate;

    // The following placeholders represent external logic from existing stored procedures
    private void containerRepairCleaningStatusGeneralUpdate(Integer eirId, Integer usuarioId) {
        logger.info("before containerRepairCleaningStatusGeneralUpdate");
        jdbcTemplate.update("EXEC sde.container_repair_cleaning_status_general_update ?, ?",
                eirId, usuarioId);
        logger.info("containerRepairCleaningStatusGeneralUpdate called for eirId = {} and usuarioId = {}", eirId, usuarioId);
    }

    private void edi322UpdateDamageForActivity(Integer eirId, Integer estimadoEmrId, Integer usuarioId) {
        jdbcTemplate.update("EXEC sde.edi322_update_damage_for_activity ?, ?, ?, ?",
                eirId,null,estimadoEmrId, usuarioId);
        logger.info("edi322UpdateDamageForActivity called for eirId = {}, estimadoEmrId = {}, usuarioId = {}",
                eirId, estimadoEmrId, usuarioId);
    }

    private Catalog findCatalogByAlias(String alias) {
        return catalogRepository.findByAliasOrNull(alias).orElse(null);
    }

    @Transactional
    public EliminarEstimadoEmrOutput eliminarEstimadoEmr(EliminarEstimadoEmrInput.Input input) {
        EliminarEstimadoEmrOutput output = new EliminarEstimadoEmrOutput();
        output.setRespResult(2); // default to failure, will set to 1 on success
        output.setRespMessage("");

        Integer estimadoEmrId = input.getEstimadoEmrId();
        Integer usuarioId = input.getUsuarioId();
        Integer languageId = input.getLanguageId();

        logger.info("Attempting to delete EstimateEmr with ID: {}", estimadoEmrId);

        try {
            // retrieve the EstimateEmr record
            EstimateEmr estimateEmr = estimateEmrRepository.findByIdAndActiveTrueOrNull(estimadoEmrId)
                    .orElseThrow(() -> new IllegalArgumentException("EstimateEmr not found or already inactive"));

            estimateEmr.setActive(false);
            estimateEmr.setDeleteEstimateDate(LocalDateTime.now());
            estimateEmr.setTraceEstimate("estimate_deleted");
            estimateEmr.setModificationDate(LocalDateTime.now());
            estimateEmr.setModificationUser(userRepository.getReferenceById(usuarioId));


            estimateEmrRepository.save(estimateEmr);

            // update all estimateEmrDetail for that estimate
            List<EstimateEmrDetail> details = estimateEmrDetailRepository.findByEstimateEmrAndActiveTrue(estimateEmr);
            for (EstimateEmrDetail d : details) {
                d.setActive(false);
                d.setTraceEstimateEmrDetail("estimate_deleted");
                d.setModificationDate(LocalDateTime.now());
                    d.setModificationUser(userRepository.getReferenceById(usuarioId));
            }
            estimateEmrDetailRepository.saveAll(details);

            // obtain references to relevant catalogs
            Catalog catTypeEstimateStructure = findCatalogByAlias("47490"); // is_type_estimate_structure
            Catalog catTypeEstimateMachinery = findCatalogByAlias("47491"); // is_type_estimate_machinery
            Catalog catActivityInsp = findCatalogByAlias("cat_43161_box_inspection"); // is_mty_activity_insp
            Catalog catActivityPti = findCatalogByAlias("cat_43161_pti"); // is_mty_activity_pti

            // retrieve the eir from estimateEmr
            Integer eirId = estimateEmr.getEir().getId();
            Eir eirObj = eirRepository.findByIdAndActiveTrueOrNull(eirId)
                    .orElseThrow(() -> new IllegalStateException("Eir not found or inactive"));

            Integer catTipoEstimadoId = estimateEmr.getCatEstimateType().getId();

            // Determine which activity to find
            Integer activityCatId = null;
            if (catTipoEstimadoId.equals(catTypeEstimateStructure != null ? catTypeEstimateStructure.getId() : -1)) {
                // structure estimate => inspection
                activityCatId = catActivityInsp != null ? catActivityInsp.getId() : -1;
            } else if (catTipoEstimadoId.equals(catTypeEstimateMachinery != null ? catTypeEstimateMachinery.getId() : -1)) {
                // machinery estimate => pti
                activityCatId = catActivityPti != null ? catActivityPti.getId() : -1;
            }

            if (activityCatId != null && activityCatId > 0) {
                // find eirActivityZone if concluded and active
                EirActivityZone eirActivityZone = eirActivityZoneRepository.findActivityZone(eirId, activityCatId).orElse(null);

                if (eirActivityZone != null) {
                    if (activityCatId.equals(catActivityInsp != null ? catActivityInsp.getId() : -1)) {
                        if (Boolean.TRUE.equals(eirActivityZone.getStructureDamagedResult())) {
                            eirActivityZone.setStructureDamagedResult(false);
                            eirActivityZone.setTracezoneActivity("del_estimatex1c");
                            eirActivityZone.setModificationDate(LocalDateTime.now());
                                eirActivityZone.setModificationUser(userRepository.getReferenceById(usuarioId));

                            eirActivityZoneRepository.save(eirActivityZone);

                            if (Boolean.TRUE.equals(eirObj.getStructureWithDamage())) {
                                eirObj.setStructureWithDamage(false);
                                eirObj.setTraceEir("del_estimatex1c");
                                eirObj.setModificationDate(LocalDateTime.now());
                                eirObj.setModificationUser(userRepository.getReferenceById(usuarioId));

                                eirRepository.save(eirObj);
                            }
                        }

                    } else if (activityCatId.equals(catActivityPti != null ? catActivityPti.getId() : -1)) {

                        boolean hasMachineryDamage = Boolean.TRUE.equals(eirActivityZone.getMachineryDamagedResult())
                                || Boolean.TRUE.equals(eirActivityZone.getResultFlagAcDamage());

                        if (hasMachineryDamage) {
                            eirActivityZone.setMachineryDamagedResult(false);
                            eirActivityZone.setResultFlagAcDamage(false);
                            eirActivityZone.setTracezoneActivity("del_estimatex2c");
                            eirActivityZone.setModificationDate(LocalDateTime.now());
                            eirActivityZone.setModificationUser(userRepository.getReferenceById(usuarioId));
                            eirActivityZoneRepository.save(eirActivityZone);

                            if (Boolean.TRUE.equals(eirObj.getMachineryWithDamage())) {
                                eirObj.setMachineryWithDamage(false);
                                eirObj.setTraceEir("del_estimatex2c");
                                eirObj.setModificationDate(LocalDateTime.now());
                                eirObj.setModificationUser(userRepository.getReferenceById(usuarioId));

                                eirRepository.save(eirObj);
                            }
                        }
                    }
                }
            }

            // mimic calls to container_repair_cleaning_status_general_update
            containerRepairCleaningStatusGeneralUpdate(eirId, usuarioId);

            // mimic calls to edi322_update_damage_for_activity
            edi322UpdateDamageForActivity(eirId, estimadoEmrId, usuarioId);

             String translatedMsg = messageLanguageRepository.fnTranslatedMessage("EMR_CHASSIS_ESTIMATE", 3, languageId);

            output.setRespMessage(translatedMsg);
            output.setRespResult(1);

            logger.info("EstimateEmr with ID {} deleted successfully.", estimadoEmrId);

        } catch (Exception e) {
            // transaction will roll back automatically on runtime exception if not caught
            logger.error("Error while deleting estimateEmrId {} : {}", input.getEstimadoEmrId(), e.getMessage(), e);
            output.setRespMessage(e.getMessage());
            output.setRespResult(2);
        }

        return output;
    }

}