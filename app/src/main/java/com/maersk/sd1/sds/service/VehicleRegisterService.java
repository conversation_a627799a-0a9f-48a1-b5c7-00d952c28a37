package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Company;
import com.maersk.sd1.common.model.Truck;
import com.maersk.sd1.common.model.User;
import com.maersk.sd1.common.repository.CompanyRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.TruckRepository;
import com.maersk.sd1.sds.dto.VehicleRegisterOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class VehicleRegisterService {

    private static final Logger logger = LogManager.getLogger(VehicleRegisterService.class);

    private final TruckRepository truckRepository;
    private final CompanyRepository companyRepository;
    private final MessageLanguageRepository messageLanguageRepository;

    @Transactional
    public VehicleRegisterOutput registerVehicle(String plate,
                                                 BigDecimal payload,
                                                 String model,
                                                 BigDecimal netWeight,
                                                 BigDecimal bruteWeight,
                                                 Integer transportCompanyId,
                                                 String companyDocument,
                                                 Boolean active,
                                                 Integer userRegistrationId,
                                                 Integer languageId) {
        VehicleRegisterOutput output = new VehicleRegisterOutput();
        output.setRespNewId(0);
        output.setRespEstado(0);
        output.setRespMensaje("");

        logger.info("Starting registerVehicle for plate: {}", plate);

        try {
            // Determine which Company ID to use
            logger.info("Determining Company ID for vehicle registration");
            Integer pCompanyId = null;
            if (companyDocument != null && !companyDocument.isBlank()) {
                try {
                    // Use findFirstByDocumentIgnoreCase to avoid the non-unique result exception
                    Company company = companyRepository.findFirstByDocumentIgnoreCase(companyDocument);
                    if (company != null) {
                        pCompanyId = company.getId();
                    }
                } catch (Exception ex) {
                    logger.warn("Error finding company by document: {}", companyDocument, ex);
                    // Continue with the next fallback option
                }
            }

            // If not found by document or not provided document, try with transportCompanyId
            logger.info("Checking transportCompanyId for vehicle registration");
            if (pCompanyId == null && transportCompanyId != null) {
                Optional<Company> oc2 = companyRepository.findById(transportCompanyId);
                if (oc2.isPresent()) {
                    pCompanyId = oc2.get().getId();
                }
            }

            // If still null, fallback to 'ETVARIOS'
            logger.info("Falling back to 'ETVARIOS' for vehicle registration");
            if (pCompanyId == null) {
                Company oc3 = companyRepository.findFirstByDocumentIgnoreCase("ETVARIOS");
                if (oc3 != null) {
                    pCompanyId = oc3.getId();
                }
            }

            logger.info("Final Company ID for vehicle registration: {}", pCompanyId);
            // Check if truck already exists by plate (case-insensitive)
            Optional<Truck> existingTruck = truckRepository.findByPlateIgnoreCase(plate);
            if (existingTruck.isPresent()) {
                // If truck already exists
                Truck foundTruck = existingTruck.get();
                output.setRespEstado(2);
                // Using messageLanguageRepository to replicate the message function
                // e.g. [ges].[fn_MensajeTraducido]('INS_VEHICULO', 1, @idioma_id)
                String repeatedMsg = messageLanguageRepository.fnTranslatedMessage("INS_VEHICULO", 1, languageId);
                // If the function returns something like "Vehicle already exists", we can add the TruckId
                repeatedMsg = repeatedMsg.replace("{IDX}", String.valueOf(foundTruck.getId()));
                output.setRespMensaje(repeatedMsg);
                output.setRespNewId(0);
            } else {
                // Insert the new truck
                Truck newTruck = new Truck();
                // ID is autogenerated in DB by default (if using identity) but in entity we do not have GenerationType.
                // We'll rely on DB auto increment. So we do not set its ID.
                newTruck.setPlate(plate);
                newTruck.setPayload(payload);
                newTruck.setModel(model);
                newTruck.setNetWeight(netWeight);
                newTruck.setBruteWeight(bruteWeight);
                if (pCompanyId != null) {
                    Company transportCompany = new Company();
                    transportCompany.setId(pCompanyId);
                    newTruck.setTransportCompany(transportCompany);
                }
                newTruck.setActive(active);
                // set registration user
                User regUser = new User();
                regUser.setId(userRegistrationId);
                newTruck.setRegistrationUser(regUser);
                newTruck.setRegistrationDate(LocalDateTime.now());

                Truck savedTruck = truckRepository.saveAndFlush(newTruck);

                output.setRespNewId(savedTruck.getId());
                output.setRespEstado(1);

                // messageLanguageRepository can replicate [ges].[fn_MensajeTraducido]('GENERAL', 9, @idioma_id)
                String successMsg = messageLanguageRepository.fnTranslatedMessage("GENERAL", 9, languageId);
                output.setRespMensaje(successMsg);
            }
        } catch (Exception ex) {
            logger.error("Error occurred in registerVehicle", ex);
            output.setRespEstado(0);
            output.setRespMensaje(ex.getMessage());
            output.setRespNewId(0);
            // Transaction will rollback due to @Transactional if runtime exception is thrown.
        }

        return output;
    }
}
