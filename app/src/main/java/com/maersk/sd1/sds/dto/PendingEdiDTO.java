package com.maersk.sd1.sds.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PendingEdiDTO {
    private Integer ediCoparnSetupId;
    private Integer ediCoparnId;
    private String originalFileName;
    private String ediCreationDate;
    private LocalDateTime registrationDate;
    private Integer shippingLineId;
    private Integer order;
    private String bookingNumber;
    private String reservationStatus;
    private Boolean hasNewUpdate;
    private Integer ediCountByMinute;
    private Integer groupNumber;
    private Integer ownerCorrelative;
}
