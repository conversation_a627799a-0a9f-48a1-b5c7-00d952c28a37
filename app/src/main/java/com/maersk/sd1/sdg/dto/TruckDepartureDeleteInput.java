package com.maersk.sd1.sdg.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

public class TruckDepartureDeleteInput {

    @Data
    public static class Input {
        @JsonProperty("eir_id")
        private Integer eirId;

        @JsonProperty("user_modification_id")
        private Integer usuarioModificacionId;

        @JsonProperty("language_id")
        private Integer idiomaId;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private TruckDepartureDeleteInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDG")
        private TruckDepartureDeleteInput.Prefix prefix;
    }
}
