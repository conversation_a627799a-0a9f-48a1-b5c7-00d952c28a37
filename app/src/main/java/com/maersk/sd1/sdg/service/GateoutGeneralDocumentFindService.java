package com.maersk.sd1.sdg.service;

import com.maersk.sd1.common.repository.ResourceRepository;
import com.maersk.sd1.sdg.dto.GateoutGeneralDocumentFindInputDTO;
import com.maersk.sd1.sdg.dto.GateoutGeneralDocumentFindOutputDTO;
import jakarta.persistence.EntityManager;
import jakarta.persistence.ParameterMode;
import jakarta.persistence.StoredProcedureQuery;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
@RequiredArgsConstructor
public class GateoutGeneralDocumentFindService {

    private static final Logger logger = LogManager.getLogger(GateoutGeneralDocumentFindService.class);

    private final ResourceRepository resourceRepository;
    private final EntityManager entityManager;

    public GateoutGeneralDocumentFindOutputDTO findGateoutGeneralDocument(GateoutGeneralDocumentFindInputDTO.Input input) {
        Long subBusinessUnitLocalId = input.getSubBusinessUnitLocalId();
        String equipmentNumber = input.getEquipmentNumber();
        String documentNumber = input.getDocumentNumber();
        Long userRegistrationId = input.getUserRegistrationId();
        Integer languajeId = input.getLanguajeId();
        String typeProcess = input.getTypeProcess();
        Integer apsId = input.getApsId();
        String apsData = input.getApsData();

        StoredProcedureQuery query = entityManager.createStoredProcedureQuery("[sdg].[gateout_general_document_find]");

        query.registerStoredProcedureParameter("sub_business_unit_local_id", BigDecimal.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("equipment_number", String.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("document_number", String.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("user_registration_id", BigDecimal.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("languaje_id", Integer.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("type_process", String.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("aps_id", Integer.class, ParameterMode.IN);
        query.registerStoredProcedureParameter("aps_data", String.class, ParameterMode.IN);

        query.setParameter("sub_business_unit_local_id", BigDecimal.valueOf(subBusinessUnitLocalId));
        query.setParameter("equipment_number", equipmentNumber);
        query.setParameter("document_number", documentNumber);
        query.setParameter("user_registration_id", BigDecimal.valueOf(userRegistrationId));
        query.setParameter("languaje_id", languajeId);
        query.setParameter("type_process", typeProcess);
        query.setParameter("aps_id", apsId);
        query.setParameter("aps_data", apsData);

        query.execute();

        // List to store all result sets, including empty ones
        List<List<Object[]>> allResults = new ArrayList<>();

        boolean hasMoreResults = true;
        while (hasMoreResults) {
            List<Object[]> resultSet = query.getResultList(); // Get current result set
            allResults.add(resultSet); // Always add the list, even if empty

            hasMoreResults = query.hasMoreResults(); // Check if another result set exists
        }

        for (int i = 0; i < allResults.size(); i++) {
            System.out.println("Result Set " + (i + 1) + ": " + (allResults.get(i).isEmpty() ? "Empty" : ""));
            for (Object[] row : allResults.get(i)) {
                System.out.println(Arrays.toString(row));
            }
        }

        return getGateoutGeneralDocumentFindOutputDTO(allResults);
    }

    public GateoutGeneralDocumentFindOutputDTO getGateoutGeneralDocumentFindOutputDTO(List<List<Object[]>> allResults) {
        GateoutGeneralDocumentFindOutputDTO dto = new GateoutGeneralDocumentFindOutputDTO();

        List<GateoutGeneralDocumentFindOutputDTO.ResultState> resultStateList = new ArrayList<>();
        List<GateoutGeneralDocumentFindOutputDTO.DocumentDetails> documentDetailsList = new ArrayList<>();
        List<GateoutGeneralDocumentFindOutputDTO.AppointmentDetails> appointmentDetailsList = new ArrayList<>();
        List<GateoutGeneralDocumentFindOutputDTO.ApsData> apsDataList = new ArrayList<>();
        List<GateoutGeneralDocumentFindOutputDTO.GateoutGeneralAppointmentValidate> gateoutGeneralAppointmentValidateList = new ArrayList<>();

        for (List<Object[]> resultSet : allResults) {
            if (!resultSet.isEmpty()) {
                Object[] firstRow = resultSet.get(0);
                if (firstRow.length == 2 && firstRow[0] instanceof Integer) {
                    // ResultState
                    for (Object[] row : resultSet) {
                        GateoutGeneralDocumentFindOutputDTO.ResultState resultState = new GateoutGeneralDocumentFindOutputDTO.ResultState();
                        resultState.setResultState((Integer) row[0]);
                        resultState.setResultMessage((String) row[1]);
                        resultStateList.add(resultState);
                    }
                } else if (firstRow.length == 26) {
                    // DocumentDetails
                    for (Object[] row : resultSet) {
                        GateoutGeneralDocumentFindOutputDTO.DocumentDetails details = new GateoutGeneralDocumentFindOutputDTO.DocumentDetails();
                        details.setIsEmptyFullId((BigDecimal) row[0]);
                        details.setEmptyFullAlias((String) row[1]);
                        details.setReferenceDocument((String) row[2]);
                        details.setVeselDetail((String) row[3]);
                        details.setOperationType((String) row[4]);
                        details.setShippingLine((String) row[5]);
                        details.setShipperName((String) row[6]);
                        details.setConsigneeName((String) row[7]);
                        details.setEquipmentId((Integer) row[8]);
                        details.setEquipmentNumber((String) row[9]);
                        details.setEquipmentSize((String) row[10]);
                        details.setEquipmentType((String) row[11]);
                        details.setIsoCodeId((Integer) row[12]);
                        details.setIsoCode((String) row[13]);
                        details.setMoveTypeId((BigDecimal) row[14]);
                        details.setSeal1Mandatory((Boolean) row[15]);
                        details.setSeal2Mandatory((Boolean) row[16]);
                        details.setSeal3Mandatory((Boolean) row[17]);
                        details.setSeal4Mandatory((Boolean) row[18]);
                        details.setDocumentoCargaId((Integer) row[19]);
                        details.setBookingId((Integer) row[20]);
                        details.setProgramacionNaveDetalleId((Integer) row[21]);
                        details.setDocumentoCargaDetalleId((Integer) row[22]);
                        details.setMercaderia((String) row[23]);
                        details.setItem((String) row[24]);
                        details.setDepotOperationAlias((String) row[25]);
                        documentDetailsList.add(details);
                    }
                } else if (firstRow.length == 8) {
                    // AppointmentDetails
                    for (Object[] row : resultSet) {
                        GateoutGeneralDocumentFindOutputDTO.AppointmentDetails details = new GateoutGeneralDocumentFindOutputDTO.AppointmentDetails();
                        details.setEquipmentNumber((String) row[0]);
                        details.setApsId((String) row[1]);
                        details.setApsExternalValue((Character) row[2]);
                        details.setApsExternalCode((Character) row[3]);
                        details.setPlanningDetailId((Integer) row[4]);
                        details.setEtd((String) row[5]);
                        details.setTruckCompanyId((Integer) row[6]);
                        details.setTruckCompanyName((String) row[7]);
                        appointmentDetailsList.add(details);
                    }
                } else if (firstRow.length == 9) {
                    // ApsData
                    for (Object[] row : resultSet) {
                        GateoutGeneralDocumentFindOutputDTO.ApsData data = new GateoutGeneralDocumentFindOutputDTO.ApsData();
                        data.setAppointmentId((Long) row[0]);
                        data.setAppointmentDate((String) row[1]);
                        data.setDocumentNumber((String) row[2]);
                        data.setDriverFullname((String) row[3]);
                        data.setDriverLicenseNumber((String) row[4]);
                        data.setTruckLicensePlate((String) row[5]);
                        data.setTypeProcess((String) row[6]);
                        data.setExternalValue((String) row[7]);
                        data.setExternalCode((String) row[8]);
                        apsDataList.add(data);
                    }
                } else if (firstRow.length == 2 && firstRow[0] instanceof String) {
                    // GateoutGeneralAppointmentValidate
                    for (Object[] row : resultSet) {
                        GateoutGeneralDocumentFindOutputDTO.GateoutGeneralAppointmentValidate validate = new GateoutGeneralDocumentFindOutputDTO.GateoutGeneralAppointmentValidate();
                        validate.setTypeProductIntegration((String) row[0]);
                        validate.setSubBusinessUnitLocalAlias((String) row[1]);
                        gateoutGeneralAppointmentValidateList.add(validate);
                    }
                }
            }
        }

        dto.setResultState(resultStateList);
        dto.setDocumentDetails(documentDetailsList);
        dto.setAppointmentDetails(appointmentDetailsList);
        dto.setApsData(apsDataList);
        dto.setGateoutGeneralAppointmentValidate(gateoutGeneralAppointmentValidateList);

        return dto;
    }
}