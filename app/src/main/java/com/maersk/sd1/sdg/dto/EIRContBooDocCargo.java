package com.maersk.sd1.sdg.dto;

import lombok.Data;

@Data
public class EIRContBooDocCargo {

    private Integer bookingGoutId;
    private String bookingNumber;
    private Integer bookingCatBookingStatusId;
    private Boolean bookingActive;
    private Integer eIRBusinessUnitId;
    private Integer eIRSubBusinessUnitId;
    private Integer eIRCatOriginId;
    private Short eIRControlAssignmentLight;
    private Integer eIRCatEmptyFullId;
    private Boolean eIRActive;
    private Integer eIRContainerId;
    private String containerNumber;
    private Integer eIRDocumentCargoGofId;
    private String cargoDocument;
    private Boolean cargoDocumentActive;
    private String eIRSeal1;
    private String eIRSeal2;
    private String eIRSeal3;
    private String eIRSeal4;
    private Integer eIRCatContainerSizeId;
    private Integer eIRCatContainerTypeId;
    private String remarkRulesName;
    private Integer catMovementId;
    
    // Constructor
    public EIRContBooDocCargo(Integer bookingGoutId, String bookingNumber, Integer bookingCatBookingStatusId, Boolean bookingActive,
                      Integer eIRBusinessUnitId, Integer eIRSubBusinessUnitId, Integer eIRCatOriginId, Short eIRControlAssignmentLight,
                      Integer eIRCatEmptyFullId, Boolean eIRActive, Integer eIRContainerId, String containerNumber,
                      Integer eIRDocumentCargoGofId, String cargoDocument, Boolean cargoDocumentActive, String eIRSeal1,
                      String eIRSeal2, String eIRSeal3, String eIRSeal4, Integer eIRCatContainerSizeId, Integer eIRCatContainerTypeId,
                      String remarkRulesName, Integer catMovementId) {

        this.bookingGoutId = bookingGoutId;
        this.bookingNumber = bookingNumber;
        this.bookingCatBookingStatusId = bookingCatBookingStatusId;
        this.bookingActive = bookingActive;
        this.eIRBusinessUnitId = eIRBusinessUnitId;
        this.eIRSubBusinessUnitId = eIRSubBusinessUnitId;
        this.eIRCatOriginId = eIRCatOriginId;
        this.eIRControlAssignmentLight = eIRControlAssignmentLight;
        this.eIRCatEmptyFullId = eIRCatEmptyFullId;
        this.eIRActive = eIRActive;
        this.eIRContainerId = eIRContainerId;
        this.containerNumber = containerNumber;
        this.eIRDocumentCargoGofId = eIRDocumentCargoGofId;
        this.cargoDocument = cargoDocument;
        this.cargoDocumentActive = cargoDocumentActive;
        this.eIRSeal1 = eIRSeal1;
        this.eIRSeal2 = eIRSeal2;
        this.eIRSeal3 = eIRSeal3;
        this.eIRSeal4 = eIRSeal4;
        this.eIRCatContainerSizeId = eIRCatContainerSizeId;
        this.eIRCatContainerTypeId = eIRCatContainerTypeId;
        this.remarkRulesName = remarkRulesName;
        this.catMovementId = catMovementId;

    }

}
