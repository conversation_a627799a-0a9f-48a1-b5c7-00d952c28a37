package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.maersk.sd1.sdg.dto.TotalOutputDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class BookingResponse {

    private List<BookingPreAllocationContainerListOutput> data;
    private List<TotalOutputDTO> total;

//    public BookingResponse(List<BookingPreAllocationContainerListOutput> data, TotalOutputDTO total) {
//        this.data = data;
//        this.total = total;
//    }
}
