package com.maersk.sd1.sdg.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdg.dto.ResponseTruckDepartureDelete;
import com.maersk.sd1.sdg.dto.TbChassisEstimate;
import com.maersk.sd1.sdg.dto.TbContainerEstimate;
import com.maersk.sd1.sdg.dto.TruckDepartureDeleteInput;
import com.maersk.sd1.sdg.repository.SdgEstimateEmrRepository;
import com.maersk.sd1.sdy.service.CheckYardIntegrationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
public class TruckDepartureDeleteService {

    //CATALOG ALIAS
    private static final String IS_GATE_IN_ALIAS = "43080";
    private static final String IS_GATE_OUT_ALIAS = "43081";
    private static final String IS_FULL_ALIAS = "43084";
    private static final String IS_EMPTY_ALIAS = "43083";
    private static final String IS_CHASSIS_DOC_STATUS_PENDING_ALIAS = "sd1_chassisdoc_status_pending";
    private static final String IS_PRE_ASSIGNED_ALIAS = "47747";
    private static final String ESTIMATE_STATUS_FINALIZED_ALIAS = "47484";
    private static final String ESTIMATE_STATUS_CREATED_ALIAS = "47547";
    private static final String ESTIMATE_STATUS_REJECTED_ALIAS = "47486";
    private static final String CHASSIS_ESTIMATE_STATUS_FINALIZED_ALIAS = "sd1_chaest_finalized";
    private static final String CHASSIS_ESTIMATE_STATUS_CREATED_ALIAS = "sd1_chaest_created";
    private static final String CHASSIS_ESTIMATE_STATUS_REJECTED_ALIAS = "sd1_chaest_rejected";
    private static final String STATE_PROGRAMMING_ALIAS = "cat_48746_programm";
    private static final String SD1_ACLOST_DISABLED_ALIAS = "sd1_aclost_disabled";

    //CONTAINER DUMMY NUMBERS
    private static final String CONTAINER_NO_CNT = "NO-CNT";
    private static final String CONTAINER_NOT_APPLICABLE = "NOT APPLICA";
    private static final String GENERAL = "GENERAL";
    private static final String TRACE_DEL_TRUCKDEPART_GOE = "del_truckdepart_goe";
    private static final String TRACE_DEL_TRUCKDEPARTURE = "del_truckdeparture";
    private static final String TRACE_DEL_TRUCKDEPARTURE_AUTO = "del_truckdep_go_auto";



    private final  ContainerRepository containerRepository;

    private final  EirRepository eirRepository;

    private final  SdgEstimateEmrRepository sdgEstimateEmrRepository;

    private final  ChassisEstimateRepository chassisEstimateRepository;

    private final  EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;

    private final  CargoDocumentDetailRepository cargoDocumentDetailRepository;

    private final  ContainerPreassignmentRepository containerPreassignmentRepository;

    private final  UserRepository userRepository;

    private final  VesselProgrammingContainerRepository vesselProgrammingContainerRepository;

    private final  BookingDetailRepository bookingDetailRepository;

    private final  TransportPlanningDetailRepository transportPlanningDetailRepository;

    private final  CatalogRepository catalogRepository;

    private final  EstimateEmrDetailRepository estimateEmrDetailRepository;

    private final  EirChassisRepository eirChassisRepository;

    private final  ChassisDocumentDetailRepository chassisDocumentDetailRepository;

    private final  BusinessUnitRepository businessUnitRepository;

    private final  ChassisBookingDocumentRepository chassisBookingDocumentRepository;

    private final  ChassisEstimateDetailRepository chassisEstimateDetailRepository;

    private final  ActivityLogRepository activityLogRepository;

    private final  MessageLanguageRepository messageLanguageRepository;

    private final  CatalogLanguageRepository catalogLanguageRepository;

    private final CheckYardIntegrationService checkYardIntegrationService;

    @Transactional
    public ResponseTruckDepartureDelete truckDepartureDelete(TruckDepartureDeleteInput.Input input) throws Exception {
        ResponseTruckDepartureDelete response = new ResponseTruckDepartureDelete();

        if (input == null) {
            response.setResponseState(0);
            response.setResponseMessage("");
            return response;
        }

        Integer eirId = input.getEirId();
        Integer subBusinessUnitLocalId = input.getSubBusinessUnitLocalId();
        Integer languageId = input.getIdiomaId();
        Integer userModificationId = input.getUsuarioModificacionId();

        Integer isGateIn = catalogRepository.findIdByAlias(IS_GATE_IN_ALIAS);
        Integer isGateOut = catalogRepository.findIdByAlias(IS_GATE_OUT_ALIAS);
        Integer isFull = catalogRepository.findIdByAlias(IS_FULL_ALIAS);
        Integer isEmpty = catalogRepository.findIdByAlias(IS_EMPTY_ALIAS);
        Integer isPreAssigned = catalogRepository.findIdByAlias(IS_PRE_ASSIGNED_ALIAS);
        Integer estimateStatusFinalized = catalogRepository.findIdByAlias(ESTIMATE_STATUS_FINALIZED_ALIAS);
        Integer estimateStatusCreated = catalogRepository.findIdByAlias(ESTIMATE_STATUS_CREATED_ALIAS);
        Integer estimateStatusRejected = catalogRepository.findIdByAlias(ESTIMATE_STATUS_REJECTED_ALIAS);
        Integer chaestStatusFinalized = catalogRepository.findIdByAlias(CHASSIS_ESTIMATE_STATUS_FINALIZED_ALIAS);
        Integer chaestStatusCreated = catalogRepository.findIdByAlias(CHASSIS_ESTIMATE_STATUS_CREATED_ALIAS);
        Integer chaestStatusRejected = catalogRepository.findIdByAlias(CHASSIS_ESTIMATE_STATUS_REJECTED_ALIAS);
        //--------- to Assign
        Integer chassisdocStatusPending = catalogRepository.findIdByAlias(IS_CHASSIS_DOC_STATUS_PENDING_ALIAS);
        Integer catStateProgramming = catalogRepository.findIdByAlias(STATE_PROGRAMMING_ALIAS);
        Integer catSd1AclostDisabled = catalogRepository.findIdByAlias(SD1_ACLOST_DISABLED_ALIAS);

        //SET NOCOUNT ON resp_state = 0, resp_message = ''
        response.setResponseState(0);
        response.setResponseMessage("");

        //BEGIN TRY
        Integer containerNoCntId = containerRepository.findIdByContainerNumber(CONTAINER_NO_CNT);
        Integer containerNotApplicableId = containerRepository.findIdByContainerNumber(CONTAINER_NOT_APPLICABLE);

        List<Integer> containerDummyIds = new ArrayList<>();
        containerDummyIds.add(containerNoCntId);
        containerDummyIds.add(containerNotApplicableId);

        //unidad_negocio_id <> 2 SET @cita_opacif_id = NULL --exclusivo para ECUADOR
        Eir eir = eirRepository.findOneById(eirId);
        User userModification = userRepository.findById(userModificationId).orElse(null);
        BusinessUnit subBusinessUnitLocal = businessUnitRepository.findById(subBusinessUnitLocalId).orElse(null);

        if (eir!=null &&eir.getCatMovement() == null) {
            response.setResponseState(2);
            response.setResponseMessage(messageLanguageRepository.findMensaje(GENERAL, 15, languageId));
            return response;
        } else {
            if (eir != null && !eir.getActive()) {
                response.setResponseState(2);
                response.setResponseMessage(messageLanguageRepository.findMensaje(GENERAL, 8, languageId));
                return response; // Dont forget 3rd resp parameter ASK
            } else {
                if (eir!=null && eir.getTruckDepartureDate() != null) {
                    response.setResponseState(2);
                    response.setResponseMessage(messageLanguageRepository.findMensaje("TRUCK_DEPARTURE", 1, languageId));
                    return response;
                }
            }
        }
        //--===== ADDING VALIDATION FOR ESTIMATES =====--
        List<Integer> containerEstimateStatusNotInList = new ArrayList<>();
        containerEstimateStatusNotInList.add(estimateStatusRejected);
        containerEstimateStatusNotInList.add(estimateStatusFinalized);
        containerEstimateStatusNotInList.add(estimateStatusCreated);
        List<TbContainerEstimate> listCntEstimate = new ArrayList<>();

        if (eir.getCatMovement().getId().equals(isGateIn) && eir.getCatEmptyFull().getId().equals(isEmpty)) {//--Gate In Empty
            //--Ask if there are active estimates
            List<EstimateEmr> estimates = sdgEstimateEmrRepository.findByEirAndCatEstimateStatusNotInListAndActiveTrue(eir.getId(), containerEstimateStatusNotInList);
            estimates.forEach(estimate -> {
                TbContainerEstimate cntEstimate = new TbContainerEstimate(
                        catalogLanguageRepository.findDescriptionByCatalogIdAndLanguageId(estimate.getCatEstimateType().getId(), languageId),
                        estimate.getId(),
                        catalogLanguageRepository.findDescriptionByCatalogIdAndLanguageId(estimate.getCatEstimateStatus().getId(), languageId)
                );
                listCntEstimate.add(cntEstimate); //Ask About TryCatch
            });
            if (!listCntEstimate.isEmpty()) {
                // Concatenate message if exists estimates
                StringBuilder concatenatedDetails = new StringBuilder();
                listCntEstimate.stream()
                        .sorted((e1, e2) -> Long.compare(e2.getEstimateId(), e1.getEstimateId()))  // Order by estimateId
                        .forEach(e -> concatenatedDetails.append(", ")
                                .append(e.getEstimateType())
                                .append(": #")
                                .append(String.format("%d", e.getEstimateId()))
                                .append(" ")
                                .append(e.getStatusEstimate()));
                // Delete first Comma and space
                if (!concatenatedDetails.isEmpty()) {
                    concatenatedDetails.delete(0, 2);
                }
                String estimatesFound = concatenatedDetails.toString();

                response.setResponseState(2);
                String respMessage = messageLanguageRepository.findMensaje("EIR", 1, languageId);
                response.setResponseMessage(respMessage.replace("{eirx}", eir.getId() + estimatesFound));
                return response;
            }
        }
        List<Integer> chassisEstimateStatusNotInList = new ArrayList<>();
        chassisEstimateStatusNotInList.add(chaestStatusCreated);
        chassisEstimateStatusNotInList.add(chaestStatusFinalized);
        chassisEstimateStatusNotInList.add(chaestStatusRejected);
        List<TbChassisEstimate> listChaEstimate = new ArrayList<>();

        if (response.getResponseState() == 0 && eir.getCatMovement().getId().equals(isGateIn) && eir.getEirChassis() != null) {
            //--Ask if there are active chassis estimates
            List<ChassisEstimate> chassisEstimates = chassisEstimateRepository.findByEirChassisIdAndNotInChaestimStatusList(eir.getEirChassis().getId(), chassisEstimateStatusNotInList);
            chassisEstimates.forEach(estimate -> {
                TbChassisEstimate chaEstimate = new TbChassisEstimate(
                        estimate.getId(),
                        catalogLanguageRepository.findDescriptionByCatalogIdAndLanguageId(estimate.getCatChaestimStatus().getId(), languageId)
                );
                listChaEstimate.add(chaEstimate); //Ask About TryCatch
            });
            if (!listChaEstimate.isEmpty()) {
                // Concatenate message if exists chassis estimates
                StringBuilder concatenatedDetails = new StringBuilder();
                listChaEstimate.stream()
                        .sorted((e1, e2) -> Long.compare(e2.getEstimateId(), e1.getEstimateId()))  // Order by estimateId
                        .forEach(e -> concatenatedDetails.append(", ")
                                .append(e.getStatusEstimate())
                                .append(": #")
                                .append(String.format("%d", e.getEstimateId())));
                // Delete first Comma if it exists
                if (!concatenatedDetails.isEmpty()) {
                    concatenatedDetails.delete(0, 2);
                }
                String estimatesFound = concatenatedDetails.toString();

                response.setResponseState(2);
                response.setResponseMessage(messageLanguageRepository.findMensaje("TRUCK_DEPARTURE", 7, languageId) + ": " + estimatesFound);
                return response;
            }
        }
        EirDocumentCargoDetail eirDocumentCargoDetail = eirDocumentCargoDetailRepository.findByEirIdAndActiveIsTrue(input.getEirId()).stream().findFirst().orElse(null);
        CargoDocumentDetail cargoDocumentDetail = new CargoDocumentDetail();
        if (response.getResponseState() == 0) {
            //BEGIN TRANSACTION
            if (eir.getCatMovement().getId().equals(isGateOut) && eir.getCatEmptyFull().getId().equals(isEmpty)
                    && !containerDummyIds.contains(eir.getContainer().getId())) {
                //1° option
                cargoDocumentDetail = cargoDocumentDetailRepository.findByIdAndActiveTrue(eirDocumentCargoDetail.getCargoDocumentDetail().getId());
                ContainerPreassignment containerPreassignment = containerPreassignmentRepository.findByCargoDocumentDetailIdAndActiveTrue(cargoDocumentDetail.getId());
                //2° option
                if ((containerPreassignment == null || containerPreassignment.getId() == null) &&
                        Objects.nonNull(cargoDocumentDetail) && Objects.nonNull(cargoDocumentDetail.getBookingDetail()))
                        containerPreassignment = containerPreassignmentRepository.findByBookingDetailIdAndContainerIdAndActiveTrueOrderByRegistrationDateDesc(cargoDocumentDetail.getBookingDetail().getId(), cargoDocumentDetail.getContainer().getId());

                if (containerPreassignment!= null && !containerPreassignment.getCatOriginPreassignment().getId().equals(isPreAssigned)) {
                    cargoDocumentDetail.setContainer(null);
                    cargoDocumentDetail.setTraceCargoDocumentDetail(TRACE_DEL_TRUCKDEPART_GOE);
                    cargoDocumentDetail.setModificationUser(userModification);
                    cargoDocumentDetail.setModificationDate(LocalDateTime.now());
                    cargoDocumentDetailRepository.save(cargoDocumentDetail);

                    //--eliminar programacion nave contenedor, si no hay EIR "activo" asociado, asi se conservaria una salida que tuviera en la misma prog_nave sea del mismo BK o no
                    Eir eirGatein = eirRepository.findByContainerIdAndVesselProgrammingDetailIdAndActiveTrue(eir.getContainer().getId(), eir.getVesselProgrammingDetail().getId());
                    if (eirGatein == null || eirGatein.getId() == null) {
                        VesselProgrammingContainer vesselProgrammingContainer = vesselProgrammingContainerRepository.findByContainer_IdAndVesselProgrammingDetail_Id(eir.getContainer().getId(), eir.getVesselProgrammingDetail().getId());
                        vesselProgrammingContainerRepository.delete(vesselProgrammingContainer);//Add optional to Fix?
                    }
                    //--desactivar lo asignado
                    containerPreassignment.setActive(false);
                    containerPreassignment.setModificationUser(userModification);
                    containerPreassignment.setModificationDate(LocalDateTime.now());
                    containerPreassignment.setTracePreassignment(TRACE_DEL_TRUCKDEPART_GOE);
                    containerPreassignmentRepository.save(containerPreassignment);

                    //--recalcula las cantidades asignadas
                    int cantAtendido = 0;
                    if(Objects.nonNull(cargoDocumentDetail) && Objects.nonNull(cargoDocumentDetail.getBookingDetail())) {
                        cantAtendido = cargoDocumentDetailRepository.countFindByBookingDetailIdAndContainerIdNotNullAndActiveTrueAndBookingDetailActiveTrueAndActiveTrue(cargoDocumentDetail.getBookingDetail().getId());
                        BookingDetail bookingDetail = bookingDetailRepository.findByIdAndActiveTrue(cargoDocumentDetail.getBookingDetail().getId());
                        bookingDetail.setAttendedQuantity(cantAtendido);
                        bookingDetail.setModificationUser(userModification);
                        bookingDetail.setModificationDate(LocalDateTime.now());
                        bookingDetail.setTraceBkDetail(TRACE_DEL_TRUCKDEPART_GOE);
                        bookingDetailRepository.save(bookingDetail);
                    }
                }
            }
            if (eir.getCatEmptyFull().getId().equals(isFull) && !containerDummyIds.contains(eir.getContainer().getId())) { //--GIF/GOF
                eirDocumentCargoDetail = eirDocumentCargoDetailRepository.findOneByEirId(eir.getId());
                cargoDocumentDetail = cargoDocumentDetailRepository.findByIdAndActiveTrue(eirDocumentCargoDetail.getCargoDocumentDetail().getId());
                VesselProgrammingContainer vesselProgrammingContainer = vesselProgrammingContainerRepository.findByContainer_IdAndVesselProgrammingDetail_IdAndActiveTrue(eir.getContainer().getId(), eir.getVesselProgrammingDetail().getId());

                if (eir.getCatMovement().getId().equals(isGateIn) && cargoDocumentDetail != null) { //--GATE IN FULL
                    cargoDocumentDetail.setReceivedQuantity(BigDecimal.valueOf(0));
                    cargoDocumentDetail.setReceivedWeight(BigDecimal.valueOf(0));
                    cargoDocumentDetail.setCatReceivedWeightMeasure(null);
                    cargoDocumentDetail.setReceivedVolume(BigDecimal.valueOf(0));
                    cargoDocumentDetail.setBalanceQuantity(BigDecimal.valueOf(0));
                    cargoDocumentDetail.setBalanceWeight(BigDecimal.valueOf(0));
                    cargoDocumentDetail.setBalanceVolume(BigDecimal.valueOf(0));
                    cargoDocumentDetail.setModificationUser(userModification);
                    cargoDocumentDetail.setModificationDate(LocalDateTime.now());
                    cargoDocumentDetail.setTraceCargoDocumentDetail("del_truckdepart_gif");
                    cargoDocumentDetailRepository.save(cargoDocumentDetail);

                    vesselProgrammingContainer.setReceivedQuantity(0);
                    vesselProgrammingContainer.setReceivedWeight(BigDecimal.valueOf(0));
                    vesselProgrammingContainer.setCatReceivedWeightMeasure(null);
                    vesselProgrammingContainer.setReceivedSeal1(null);
                    vesselProgrammingContainer.setReceivedSeal2(null);
                    vesselProgrammingContainer.setReceivedSeal3(null);
                    vesselProgrammingContainer.setReceivedSeal4(null);
                    vesselProgrammingContainer.setModificationUser(userModification);
                    vesselProgrammingContainer.setModificationDate(LocalDateTime.now());
                    vesselProgrammingContainer.setTraceProgVesCnt("del_truckdepart_gif");
                    vesselProgrammingContainerRepository.save(vesselProgrammingContainer);
                }
                if (eir.getCatMovement().getId().equals(isGateOut) && cargoDocumentDetail != null) { //--GATE OUT FULL
                    cargoDocumentDetail.setDispatchedQuantity(BigDecimal.valueOf(0));
                    cargoDocumentDetail.setDispatchedWeight(null);
                    cargoDocumentDetail.setDispatchedVolume(BigDecimal.valueOf(0));
                    cargoDocumentDetail.setBalanceQuantity(BigDecimal.valueOf(1));
                    cargoDocumentDetail.setBalanceWeight(cargoDocumentDetail.getReceivedWeight());
                    cargoDocumentDetail.setBalanceVolume(BigDecimal.valueOf(0));
                    cargoDocumentDetail.setCatDispatchedWeightMeasure(null);
                    cargoDocumentDetail.setModificationUser(userModification);
                    cargoDocumentDetail.setModificationDate(LocalDateTime.now());
                    cargoDocumentDetail.setTraceCargoDocumentDetail("del_truckdepart_gof");
                    cargoDocumentDetailRepository.save(cargoDocumentDetail);

                    vesselProgrammingContainer.setDispatchedSeal1(null);
                    vesselProgrammingContainer.setDispatchedSeal2(null);
                    vesselProgrammingContainer.setDispatchedSeal3(null);
                    vesselProgrammingContainer.setDispatchedSeal4(null);
                    vesselProgrammingContainer.setModificationUser(userModification);
                    vesselProgrammingContainer.setModificationDate(LocalDateTime.now());
                    vesselProgrammingContainer.setTraceProgVesCnt("del_truckdepart_gof");
                    vesselProgrammingContainerRepository.save(vesselProgrammingContainer);
                }

                TransportPlanningDetail transportPlanningDetail = transportPlanningDetailRepository.findOneById(eir.getTransportPlanningDetailFull().getId());
                transportPlanningDetail.setCatTrkPlanningState(catalogRepository.findById(catStateProgramming).orElse(null));
                transportPlanningDetail.setModificationUser(userModification);
                transportPlanningDetail.setModificationDate(LocalDateTime.now());
                transportPlanningDetailRepository.save(transportPlanningDetail);
            }
            if (eir.getCatMovement().getId().equals(isGateIn) && eir.getCatEmptyFull().getId().equals(isEmpty)) { //--Gate In Empty
                List<EstimateEmr> estimates = sdgEstimateEmrRepository.findByEirAndCatEstimateStatusInListAndActiveTrue(eir.getId(), containerEstimateStatusNotInList);
                if (!estimates.isEmpty()) {
                    estimates.forEach(estimateEmr -> {
                        List<EstimateEmrDetail> estimateEmrDetailList = estimateEmrDetailRepository.findByEstimateEmr_IdAndActiveTrue(estimateEmr.getId());
                        if (!estimateEmrDetailList.isEmpty()) {
                            estimateEmrDetailList.forEach(estimateEmrDetail -> {
                                estimateEmrDetail.setActive(false);
                                estimateEmrDetail.setTraceEstimateEmrDetail("del_truckdepart_ec");
                                estimateEmrDetail.setModificationUser(userModification);
                                estimateEmrDetail.setModificationDate(LocalDateTime.now());
                                estimateEmrDetailRepository.save(estimateEmrDetail);
                            });
                        }
                        estimateEmr.setActive(false);
                        estimateEmr.setDeleteEstimateDate(LocalDateTime.now());
                        estimateEmr.setTraceEstimate("del_truckdepart_ec");
                        estimateEmr.setModificationUser(userModification);
                        estimateEmr.setModificationDate(LocalDateTime.now());
                        sdgEstimateEmrRepository.save(estimateEmr);
                    });
                }
            }
            eir.setActive(false);
            eir.setTraceEir(TRACE_DEL_TRUCKDEPARTURE);
            eir.setModificationUser(userModification);
            eir.setModificationDate(LocalDateTime.now());
            eir.setDeleteUser(userModification);
            eir.setDeleteDate(LocalDateTime.now());
            eirRepository.save(eir);
            if(Objects.nonNull(eirDocumentCargoDetail)) {
                eirDocumentCargoDetail.setActive(false);
                eirDocumentCargoDetail.setModificationUser(userModification);
                eirDocumentCargoDetail.setModificationDate(LocalDateTime.now());
                eirDocumentCargoDetail.setTraceEirDocDetail(TRACE_DEL_TRUCKDEPARTURE);
                eirDocumentCargoDetailRepository.save(eirDocumentCargoDetail);
            }

            if (eir.getEirChassis() != null && response.getResponseState() == 0) { //--GI/GO CHASSIS
                    Eir eirAux = null;
                    //--find register automatic
                    if (eir.getEirChassis().getChassis() != null &&
                            ((eir.getCatMovement().getId().equals(isGateIn) && !eir.getFlagChassisStayed()) ||
                                    (eir.getCatMovement().getId().equals(isGateOut) && !eir.getFlagChassisPickup()))) {
                        eirAux = eirRepository.findByContainerIdAndLocalSubBusinessUnitIdAndCatMovementIdAndTruckIdAndDriverPersonIdAndTruckArrivalDateBetweenAndActiveTrue//()
                                (containerNotApplicableId, subBusinessUnitLocal.getId(), eir.getCatMovement().getId().equals(isGateIn) ? isGateOut : isGateIn,
                                        eir.getTruck().getId(), eir.getDriverPerson().getId(), eir.getTruckArrivalDate().minusMinutes(5),
                                        eir.getTruckArrivalDate().plusMinutes(5));
                    }

                    if (eir.getCatMovement().getId().equals(isGateOut) && Objects.nonNull(eirAux) && eirAux.getEirChassis().getId() != null) {//--delete automatic register "gate in"
                        eirAux.getEirChassis().getChassisDocumentDetail().setCatStatusChassis(catalogRepository.findById(chassisdocStatusPending).orElse(null));
                        eirAux.getEirChassis().getChassisDocumentDetail().setModificationUser(userModification);
                        eirAux.getEirChassis().getChassisDocumentDetail().setModificationDate(LocalDateTime.now());
                        eirAux.getEirChassis().getChassisDocumentDetail().setTraceChassisDocDetail(TRACE_DEL_TRUCKDEPARTURE_AUTO);
                        chassisDocumentDetailRepository.save(eirAux.getEirChassis().getChassisDocumentDetail());

                        eirAux.setActive(false);
                        eirAux.setTraceEir(TRACE_DEL_TRUCKDEPARTURE_AUTO);
                        eirAux.setModificationUser(userModification);
                        eirAux.setModificationDate(LocalDateTime.now());
                        eirAux.setDeleteUser(userModification);
                        eirAux.setDeleteDate(LocalDateTime.now());
                        eirRepository.save(eirAux);

                        eirAux.getEirChassis().setActive(false);
                        eirAux.getEirChassis().setModificationUser(userModification);
                        eirAux.getEirChassis().setModificationDate(LocalDateTime.now());
                        eirAux.getEirChassis().setTraceEirChassis(TRACE_DEL_TRUCKDEPARTURE_AUTO);
                        eirChassisRepository.save(eirAux.getEirChassis());
                    }
                    if (eir.getCatMovement().getId().equals(isGateIn) && eir.getEirChassis().getChassisDocumentDetail().getId() != null) {
                        eir.getEirChassis().getChassisDocumentDetail().setCatStatusChassis(catalogRepository.findById(chassisdocStatusPending).orElse(null));
                        eir.getEirChassis().getChassisDocumentDetail().setModificationUser(userModification);
                        eir.getEirChassis().getChassisDocumentDetail().setModificationDate(LocalDateTime.now());
                        eir.getEirChassis().getChassisDocumentDetail().setTraceChassisDocDetail("del_truckdepart_gi");
                        chassisDocumentDetailRepository.save(eir.getEirChassis().getChassisDocumentDetail());//--GI
                    }
                    if (eir.getCatMovement().getId().equals(isGateOut) && Objects.nonNull(eir.getEirChassis())
                           && Objects.nonNull(eir.getEirChassis().getChassisDocumentDetail()) && eir.getEirChassis().getChassisDocumentDetail().getId() != null) {
                        eir.getEirChassis().getChassisDocumentDetail().setCatStatusChassis(catalogRepository.findById(chassisdocStatusPending).orElse(null));
                        eir.getEirChassis().getChassisDocumentDetail().setChassis(null);
                        eir.getEirChassis().getChassisDocumentDetail().setModificationUser(userModification);
                        eir.getEirChassis().getChassisDocumentDetail().setModificationDate(LocalDateTime.now());
                        eir.getEirChassis().getChassisDocumentDetail().setTraceChassisDocDetail("del_truckdepart_go");
                        chassisDocumentDetailRepository.save(eir.getEirChassis().getChassisDocumentDetail());

                        //assert documentoCargaDetalle != null; //ask about assert CAED
                        Integer qchaAttended = null;
                        if(Objects.nonNull(cargoDocumentDetail) && Objects.nonNull(cargoDocumentDetail.getBookingDetail())) {
                            qchaAttended = chassisDocumentDetailRepository.countFindByChassisBookingDocumentIdAndChassisIdNotNullAndActiveTrue(cargoDocumentDetail.getBookingDetail().getId());
                        }
                        eir.getEirChassis().getChassisDocumentDetail().getChassisBookingDocument().setAttendedQuantity(qchaAttended == null ? 0 : qchaAttended);
                        eir.getEirChassis().getChassisDocumentDetail().getChassisBookingDocument().setModificationUser(userModification);
                        eir.getEirChassis().getChassisDocumentDetail().getChassisBookingDocument().setModificationDate(LocalDateTime.now());
                        chassisBookingDocumentRepository.save(eir.getEirChassis().getChassisDocumentDetail().getChassisBookingDocument());//--GO
                    }

                    if (eir.getCatMovement().getId().equals(isGateIn)) { //--GI
                        List<ChassisEstimate> estimates = chassisEstimateRepository.findByEirChassisIdAndInChaestimStatusList(eir.getEirChassis().getId(), chassisEstimateStatusNotInList);
                        if (!estimates.isEmpty()) {
                            estimates.forEach(chaEstimate -> {
                                List<ChassisEstimateDetail> chassisEstimateDetailList = chassisEstimateDetailRepository.findByChassisEstimateIdAndActiveTrue(chaEstimate.getId());
                                if (!chassisEstimateDetailList.isEmpty()) {
                                    chassisEstimateDetailList.forEach(chassisEstimateDetail -> {
                                        chassisEstimateDetail.setActive(false);
                                        chassisEstimateDetail.setEliminationDetailUser(userModification);
                                        chassisEstimateDetail.setEliminationDetailDate(LocalDateTime.now());
                                        chassisEstimateDetail.setTraceChassisEstimateDetail("del_eir_08");
                                        chassisEstimateDetail.setModificationUser(userModification);
                                        chassisEstimateDetail.setModificationDate(LocalDateTime.now());
                                        chassisEstimateDetailRepository.save(chassisEstimateDetail);
                                    });
                                }
                                chaEstimate.setActive(false);
                                chaEstimate.setEliminationUser(userModification);
                                chaEstimate.setEliminationDate(LocalDateTime.now());
                                chaEstimate.setTraceChassisEstimate("del_eir_08");
                                chaEstimate.setModificationUser(userModification);
                                chaEstimate.setModificationDate(LocalDateTime.now());
                                chassisEstimateRepository.save(chaEstimate);
                            });
                        }
                    }
                    eir.getEirChassis().setActive(false);
                    eir.getEirChassis().setModificationUser(userModification);
                    eir.getEirChassis().setModificationDate(LocalDateTime.now());
                    eir.getEirChassis().setTraceEirChassis(TRACE_DEL_TRUCKDEPARTURE);
                    eirChassisRepository.save(eir.getEirChassis());

                    if (eir.getCatMovement().getId().equals(isGateIn) && eirAux != null && eirAux.getEirChassis() != null) { //--delete automatic register "gate out //ask about Assert"
                        eirAux.getEirChassis().getChassisDocumentDetail().setCatStatusChassis(catalogRepository.findById(chassisdocStatusPending).orElse(null));
                        eirAux.getEirChassis().getChassisDocumentDetail().setChassis(null);
                        eirAux.getEirChassis().getChassisDocumentDetail().setModificationUser(userModification);
                        eirAux.getEirChassis().getChassisDocumentDetail().setModificationDate(LocalDateTime.now());
                        eirAux.getEirChassis().getChassisDocumentDetail().setTraceChassisDocDetail("del_truckdep_gi_auto");
                        chassisDocumentDetailRepository.save(eirAux.getEirChassis().getChassisDocumentDetail());

                        Integer qchaAttendedAux = chassisDocumentDetailRepository.countFindByChassisBookingDocumentIdAndChassisIdNotNullAndActiveTrue(eirAux.getEirChassis().getChassisDocumentDetail().getChassisBookingDocument().getId());
                        eirAux.getEirChassis().getChassisDocumentDetail().getChassisBookingDocument().setAttendedQuantity(qchaAttendedAux);
                        eirAux.getEirChassis().getChassisDocumentDetail().getChassisBookingDocument().setModificationUser(userModification);
                        eirAux.getEirChassis().getChassisDocumentDetail().getChassisBookingDocument().setModificationDate(LocalDateTime.now());
                        chassisBookingDocumentRepository.save(eirAux.getEirChassis().getChassisDocumentDetail().getChassisBookingDocument());

                        eirAux.setActive(false);
                        eirAux.setTraceEir("del_truckdep_gi_auto");
                        eirAux.setModificationUser(userModification);
                        eirAux.setModificationDate(LocalDateTime.now());
                        eirAux.setDeleteUser(userModification);
                        eirAux.setDeleteDate(LocalDateTime.now());
                        eirRepository.save(eirAux);

                        eirAux.getEirChassis().setActive(false);
                        eirAux.getEirChassis().setModificationUser(userModification);
                        eirAux.getEirChassis().setModificationDate(LocalDateTime.now());
                        eirChassisRepository.save(eirAux.getEirChassis());
                    }
            }
            //--======================= DISABLED ACTIVITY LOG ============================================
            ActivityLog activityLog = activityLogRepository.findByEirNumber(eir.getId());

            if (activityLog != null){
                activityLog.setActive('0');
                activityLog.setRetryable(false);
                activityLog.setCatStatus(catalogRepository.findById(catSd1AclostDisabled).orElse(null));
                activityLog.setModificationUser(userModification);
                activityLog.setModificationDate(LocalDateTime.now());
                activityLogRepository.save(activityLog);
            }
            //COMMIT TRANSACTION
            response.setResponseState(1);
            response.setResponseMessage(messageLanguageRepository.findMensaje(GENERAL, 7, languageId));

            //--======================= DISABLE SDY MOVEMENT INSTRUCTION ============================================
            if (eir.getCatMovement().getId().equals(isGateOut)) {
                response.setResponseFlagRemoveWorkOrder(checkYardIntegrationService.checkYardIntegration(subBusinessUnitLocal.getBusinesUnitAlias(), "gateout"));
            }
        }
        return response;
    }
}
