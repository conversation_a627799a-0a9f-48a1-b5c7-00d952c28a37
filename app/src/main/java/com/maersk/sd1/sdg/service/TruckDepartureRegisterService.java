package com.maersk.sd1.sdg.service;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import jakarta.persistence.EntityManager;
import jakarta.persistence.EntityNotFoundException;
import jakarta.persistence.PersistenceException;
import org.json.JSONArray;
import org.json.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.sdg.controller.dto.SdgTruckDepartureRegisterInput;
import com.maersk.sd1.sdg.controller.dto.SdgTruckDepartureRegisterOutput;
import com.maersk.sd1.sdg.dto.TbEirChassisDto;
import com.maersk.sd1.sdg.repository.*;
import com.maersk.sd1.sdg.controller.dto.ResponseTruckDepartureRegisterBeforeYard;
import com.maersk.sd1.sdg.controller.dto.SdgTruckDepartureRegisterBeforeYardInput;


@Service
public class TruckDepartureRegisterService implements ApplicationContextAware {
    public static final String PRC_FULL_GI_GO = "PRC_FULL_GI_GO";
    public static final String CNTX = "{CNTX}";
    public static final String RSTX = "{RSTX}";
    public static final String TRUCKDEPA_RECH_CHGO_2 = "truckdepa_rech_chgo2";
    public static final String TRUCKDEPA_RECH_CHGO_1 = "truckdepa_rech_chgo1";
    public static final String EIRX = "{EIRX}";
    public static final String SALIDA_CAMION = "SALIDA_CAMION";
    public static final String RESULT = "result";
    public static final String GRALTRUCKDEPA_RECH_2 = "graltruckdepa_rech2";
    public static final String IS_CORRECT = "isCorrect";
    public static final String BAD_RESPONSE = "Bad Response";
    public static final String INTEGRATION_DATA = "integration_data";
    public static final String BOOKING_NUMBER = "{BOOKING_NUMBER}";
    public static final String INS_CHASSIS = "INS_CHASSIS";

    private final EntityManager entityManager;

    private ApplicationContext context;

    private final EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;
    private final ContainerRepository containerRepository;
    private final CatalogRepository catalogRepository;
    private final SdgEirRepository eirRepository;
    private final EirMultipleRepository eirMultipleRepository;
    private final BusinessUnitRepository businessUnitRepository;
    private final SdgStockEmptyRepository sdgStockEmptyRepository;
    private final SdgStockFullRepository sdgStockFullRepository;
    private final MessageLanguageService messageLanguageService;
    private final SdgEirChassisRepository eirChassisRepository;
    private final SdgChassisRepository chassisRepository;
    private final SdgStockChassisRepository stockChassisRepository;
    private final UserRepository userRepository;
    private final SystemRuleRepository systemRuleRepository;
    private final ContainerRestrictionRepository containerRestrictionRepository;
    private final FgisInspectionRepository fgisInspectionRepository;
    private final EirDocumentCargoDetailRepository eirCargoDetailRepository;
    private final AttachmentRepository attachmentRepository;
    private final ContainerPreassignmentRepository containerPreassignmentRepository;
    private final SdsEirPhotoRepository sdsEirPhotoRepository;
    private final SdfEirPhotoRepository sdfEirPhotoRepository;
    private final ChassisDocumentDetailRepository chassisDocumentDetailRepository;
    private final ChassisBookingDocumentRepository chassisBookingDocumentRepository;
    private final CargoDocumentDetailRepository cargoDocumentDetailRepository;
    private final VesselProgrammingContainerRepository vesselProgrammingContainerRepository;
    private final TransportPlanningDetailRepository transportPlanningDetailRepository;
    private final CompanyRepository companyRepository;
    private final EirSendAppeirRepository eirSendAppeirRepository;
    private final EirNotificationRepository eirNotificationRepository;
    private final TruckDepartureRegisterBeforeYardService truckDepartureRegisterBeforeYardService;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Value("${msk.api.apiUserLogin.sdy.loginUrl}")
    private String loginUrl;

    @Value("${msk.api.apiUserLogin.sdy.user}")
    private String user;

    @Value("${msk.api.apiUserLogin.sdy.password}")
    private String password;

    @Value("${msk.api.apiUserLogin.sdy.system}")
    private String system;

    @Value("${msk.api.apiUserLogin.sdy.gateInUrl}")
    private String gateInUrl;

    @Value("${msk.api.apiUserLogin.sdy.gateOutUrl}")
    private String gateOutUrl;
    private final StockFullRepository stockFullRepository;

    public TruckDepartureRegisterService(EirMultipleRepository eirMultipleRepository, EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository, ContainerRepository containerRepository, CatalogRepository catalogRepository, SdgEirRepository eirRepository, BusinessUnitRepository businessUnitRepository, TruckDepartureRegisterBeforeYardService truckDepartureRegisterBeforeYardService, ContainerRestrictionRepository containerRestrictionRepository, ChassisBookingDocumentRepository chassisBookingDocumentRepository, SdgStockEmptyRepository sdgStockEmptyRepository, CargoDocumentDetailRepository cargoDocumentDetailRepository, EirNotificationRepository eirNotificationRepository, FgisInspectionRepository fgisInspectionRepository, SdgStockFullRepository sdgStockFullRepository, UserRepository userRepository, MessageLanguageService messageLanguageService, SystemRuleRepository systemRuleRepository, AttachmentRepository attachmentRepository, SdgStockChassisRepository stockChassisRepository, ChassisDocumentDetailRepository chassisDocumentDetailRepository, EirSendAppeirRepository eirSendAppeirRepository, SdgEirChassisRepository eirChassisRepository, SdfEirPhotoRepository sdfEirPhotoRepository, VesselProgrammingContainerRepository vesselProgrammingContainerRepository, TransportPlanningDetailRepository transportPlanningDetailRepository, CompanyRepository companyRepository, SdgChassisRepository chassisRepository, EirDocumentCargoDetailRepository eirCargoDetailRepository, SdsEirPhotoRepository sdsEirPhotoRepository, ContainerPreassignmentRepository containerPreassignmentRepository, EntityManager entityManager,
                                         StockFullRepository stockFullRepository) {
        this.eirMultipleRepository = eirMultipleRepository;
        this.eirDocumentCargoDetailRepository = eirDocumentCargoDetailRepository;
        this.containerRepository = containerRepository;
        this.catalogRepository = catalogRepository;
        this.eirRepository = eirRepository;
        this.businessUnitRepository = businessUnitRepository;
        this.truckDepartureRegisterBeforeYardService = truckDepartureRegisterBeforeYardService;
        this.containerRestrictionRepository = containerRestrictionRepository;
        this.chassisBookingDocumentRepository = chassisBookingDocumentRepository;
        this.sdgStockEmptyRepository = sdgStockEmptyRepository;
        this.cargoDocumentDetailRepository = cargoDocumentDetailRepository;
        this.eirNotificationRepository = eirNotificationRepository;
        this.fgisInspectionRepository = fgisInspectionRepository;
        this.sdgStockFullRepository = sdgStockFullRepository;
        this.userRepository = userRepository;
        this.messageLanguageService = messageLanguageService;
        this.systemRuleRepository = systemRuleRepository;
        this.attachmentRepository = attachmentRepository;
        this.stockChassisRepository = stockChassisRepository;
        this.chassisDocumentDetailRepository = chassisDocumentDetailRepository;
        this.eirSendAppeirRepository = eirSendAppeirRepository;
        this.eirChassisRepository = eirChassisRepository;
        this.sdfEirPhotoRepository = sdfEirPhotoRepository;
        this.vesselProgrammingContainerRepository = vesselProgrammingContainerRepository;
        this.transportPlanningDetailRepository = transportPlanningDetailRepository;
        this.companyRepository = companyRepository;
        this.chassisRepository = chassisRepository;
        this.eirCargoDetailRepository = eirCargoDetailRepository;
        this.sdsEirPhotoRepository = sdsEirPhotoRepository;
        this.containerPreassignmentRepository = containerPreassignmentRepository;
        this.entityManager = entityManager;
        this.stockFullRepository = stockFullRepository;
    }

    @Transactional
    public SdgTruckDepartureRegisterOutput register(SdgTruckDepartureRegisterInput.Root inputRoot) {


        SdgTruckDepartureRegisterOutput response = new SdgTruckDepartureRegisterOutput();
        if (inputRoot == null) {
            return response;
        }

        LocalDateTime truckOutDateZh = null;
        Integer isEirManual = catalogRepository.findIdByAlias(Parameter.IS_EIR_MANUAL);
        Integer isGateIn = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEIN_ALIAS);
        Integer isGateOut = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS);
        Integer isFullId = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS);
        Integer isEmpty = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS);
        Integer isContainer = catalogRepository.findIdByAlias(Parameter.EQUIPMENT_CATEGORY_CONTAINER);
        Integer isChassis = catalogRepository.findIdByAlias(Parameter.EQUIPMENT_CATEGORY_CHASSIS);
        Integer isPlanningPending = catalogRepository.findIdByAlias(Parameter.IS_PLANNING_PENDING);
        Integer isPlanningDone = catalogRepository.findIdByAlias(Parameter.IS_PLANNING_DONE);
        Integer isMeasureWeightKg = catalogRepository.findIdByAlias(Parameter.CATALOG_MEASURE_WEIGHT_KG_ALIAS);
        Integer isDocChassisCompleted = catalogRepository.findIdByAlias(Parameter.IS_DOC_CHASSIS_COMPLETED);
        Integer isDocChassisPending = catalogRepository.findIdByAlias(Parameter.CATALOG_CHASSIS_DOCUMENT_PENDING);
        Integer isPreAllocation = catalogRepository.findIdByAlias(Parameter.CATALOG_PRE_ALLOCATED_ORIGIN_PRE_ALLOCATED_ALIAS);
        Integer isOperativeContainer = catalogRepository.findIdByAlias(Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_OK);
        Integer isOperativeChassis = catalogRepository.findIdByAlias(Parameter.IS_OPERATIVE_CHASSIS);
        Integer isOperativeDirtyContainer = catalogRepository.findIdByAlias(Parameter.IS_OPERATIVE_DIRTY_CONTAINER);
        Integer isGoCustomDelivery = catalogRepository.findIdByAlias(Parameter.CATALOG_CONTAINER_MOVEMENT_CUSTOM_DELIVERY_ALIAS);

        Integer containerNoCntId = containerRepository.findByContainerNumber(Parameter.CONTAINER_NO_CNT).getId();
        Integer containerNotApplicableId = containerRepository.findByContainerNumber(Parameter.CONTAINER_NOT_APPLICA).getId();
        Integer eirNotificationPending = catalogRepository.findIdByAlias(Parameter.EIR_NOTIFICATION_PENDING);

        SdgTruckDepartureRegisterInput.Input input = inputRoot.getSdg().getInput();

        Optional<User> userRegistrationOptional = userRepository.findById(input.getUserRegistrationId());
        User userRegistration;
        userRegistration = userRegistrationOptional.orElse(null);
        String remarks = formatString(input.getRemarks());
        String seal1 = formatString(input.getSeal1());
        String seal2 = formatString(input.getSeal2());
        String seal3 = formatString(input.getSeal3());
        String seal4 = formatString(input.getSeal4());

        Integer containerId = input.getContainerId();
        Integer chassisId = input.getChassisId();

        Integer subBusinessUnitId = businessUnitRepository.findParentBusinessUnitId(input.getSubBusinessUnitLocalId());

        Integer eirMultipleId = null;
        Integer catMovementId;
        Integer catEmptyFullId = null;
        Integer eirChassisId = null;
        String bookingNumber = null;
        Integer containerEirId = null;
        Integer vehicleId = null;
        Integer driverId = null;
        LocalDateTime truckArrivalDate = null;
        Boolean flagChassisStayed = null;
        Integer catOriginId = null;
        Integer businessUnitId = null;

        Integer eirAuxId = null;
        List<EirMultiple> eirMultipleList = eirMultipleRepository.findByEirIdAndActiveIsTrue(input.getEirId());
        if (!eirMultipleList.isEmpty()) {
            eirMultipleId = eirMultipleList.getFirst().getId().getEirMultipleId();

            Eir eir;
            eir = eirMultipleList.getFirst().getEir();
            catMovementId = eir.getCatMovement().getId();
            catEmptyFullId = eir.getCatEmptyFull().getId();
            if (eir.getEirChassis() != null) {
                eirChassisId = eir.getEirChassis().getId();
            }
            if (eir.getBookingGout() != null) {
                bookingNumber = eir.getBookingGout().getBookingNumber();
            }
            containerEirId = eir.getContainer().getId();
            vehicleId = eir.getTruck().getId();
            driverId = eir.getDriverPerson().getId();
            truckArrivalDate = eir.getTruckArrivalDate();
            flagChassisStayed = eir.getFlagChassisStayed();
            catOriginId = Optional.ofNullable(eir.getCatOrigin()).map(Catalog::getId).orElse(null);
            businessUnitId = eir.getBusinessUnit().getId();

        } else {
            Eir eir;
            eir = eirRepository.findOneById(input.getEirId());
            if (eir != null) {
                catMovementId = eir.getCatMovement().getId();
                catEmptyFullId = eir.getCatEmptyFull().getId();
                if (eir.getEirChassis() != null) {
                    eirChassisId = eir.getEirChassis().getId();
                }
                if (eir.getBookingGout() != null) {
                    bookingNumber = eir.getBookingGout().getBookingNumber();
                }

                containerEirId = eir.getContainer().getId();
                vehicleId = eir.getTruck().getId();
                driverId = eir.getDriverPerson().getId();
                truckArrivalDate = eir.getTruckArrivalDate();
                flagChassisStayed = eir.getFlagChassisStayed();
                catOriginId = Optional.ofNullable(eir.getCatOrigin()).map(Catalog::getId).orElse(null);
                businessUnitId = eir.getBusinessUnit().getId();
            } else {
                catMovementId = null;
            }
        }

        if (containerId == null) {
            containerId = containerEirId;
        }

        if (containerId != null
                && !List.of(containerNotApplicableId, containerNoCntId).contains(containerId)
                && Objects.equals(catMovementId, isGateOut)) {

            if (Objects.equals(catEmptyFullId, isEmpty)) {
                eirAuxId = sdgStockEmptyRepository.getEirAuxByEirId(input.getEirId());
            }

            if (Objects.equals(catEmptyFullId, isFullId)) {
                eirAuxId = sdgStockFullRepository.getEirAuxByEirId(input.getEirId());
            }

            if (eirAuxId != null) {
                response.setRespResult(2);
                response.setResultMessage(messageLanguageService.getMessage(PRC_FULL_GI_GO, 34, input.getLanguageId(),
                        Map.of(EIRX, eirAuxId.toString(), "EIRY{}", input.getEirId().toString())));
                return response;
            }
        }

        Integer documentChassisDetailId = null;
        Integer chassis2Id = null;
        Integer eirChassisAuxId = null;
        Integer documentChassisDetailAuxId = null;

        if (eirChassisId != null) {
            eirAuxId = null;
            Optional<EirChassis> eirChassis = eirChassisRepository.findById(eirChassisId);
            if (eirChassis.isPresent()) {
                documentChassisDetailId = eirChassis.get().getChassisDocumentDetail().getId();
                chassis2Id = eirChassis.get().getChassis().getId();
            }

            if (chassis2Id != null && catMovementId.equals(isGateIn) && Boolean.FALSE.equals(flagChassisStayed)) {
                LocalDateTime startDate = truckArrivalDate.minusMinutes(5);
                LocalDateTime endDate = truckArrivalDate.plusMinutes(5);
                Eir eir = eirRepository.findEirByChassisId(containerNotApplicableId,
                        input.getSubBusinessUnitLocalId(),
                        isGateOut, vehicleId, driverId, startDate, endDate, chassis2Id);

                if (eir != null) {
                    eirAuxId = eir.getId();
                    eirChassisAuxId = eir.getEirChassis().getId();
                    documentChassisDetailAuxId = eir.getEirChassis().getChassisDocumentDetail().getId();
                }

            }

            if (chassis2Id != null && catMovementId.equals(isGateOut)) {
                eirAuxId = eirRepository.findEirIdByEirChassisId(eirChassisId);

                if (eirAuxId != null) {
                    response.setRespResult(2);
                    response.setResultMessage(messageLanguageService.getMessage(PRC_FULL_GI_GO, 34, input.getLanguageId(),
                            Map.of(EIRX, eirAuxId.toString(), "EIRY{}", input.getEirId().toString())));
                    return response;
                }

            }


            String chassisNumberANT = "";
            if (chassis2Id != null && response.getResultMessage() == null) {
                Optional<Chassis> chassisOptional = chassisRepository.findById(chassis2Id);
                if (chassisOptional.isPresent()) {
                    chassisNumberANT = chassisOptional.get().getChassisNumber();
                }

                Integer eirChassisReferences = 0;
                if (!chassisNumberANT.equalsIgnoreCase(input.getNewChassisNumber())) {
                    Integer chassisIdAux = null;
                    chassisIdAux = chassisRepository.findChassisId(chassis2Id, input.getNewChassisNumber());
                    eirChassisReferences = eirChassisRepository.countEirChassisReferencesByChassisId(chassisId);

                    if (chassisIdAux != null) {
                        response.setRespResult(2);
                        response.setResultMessage(messageLanguageService.getMessage(INS_CHASSIS, 1, input.getLanguageId(),
                                Map.of("{IDX}", chassisIdAux.toString())));
                        return response;
                    }

                    if (Objects.nonNull(eirChassisReferences) && eirChassisReferences > 2) {
                        response.setRespResult(2);
                        response.setResultMessage(messageLanguageService.getMessage(INS_CHASSIS, 2, input.getLanguageId()));
                        return response;
                    } else {
                        Integer eirChassisValidateId = 0;
                        if (Objects.equals(catMovementId, isGateIn)) {
                            eirChassisValidateId = stockChassisRepository.findEirChassisGateOutIdByEirChassisId(eirChassisId);
                        }
                        if (Objects.equals(catMovementId, isGateOut)) {
                            eirChassisValidateId = stockChassisRepository.findEirChassisGateIntIdByEirChassisId(eirChassisId);
                        }

                        Integer chassisIdTemp = eirChassisRepository.findChassisIdByEirChassisId(eirChassisValidateId);

                        if (eirChassisReferences == 2 && eirChassisValidateId != null && eirChassisValidateId != 0 && !Objects.equals(chassis2Id, chassisIdTemp)) {
                            response.setRespResult(2);
                            response.setResultMessage(messageLanguageService.getMessage(INS_CHASSIS, 2, input.getLanguageId()));
                            return response;

                        }
                    }

                    if (response.getResultMessage() == null && chassisOptional.isPresent()) {
                        Chassis chassisEdit = chassisOptional.get();
                        chassisEdit.setChassisNumber(input.getNewChassisNumber());
                        chassisEdit.setTraceChassis("upd_chass_truck_dep");
                        chassisEdit.setModificationDate(LocalDateTime.now());
                        Optional<User> userUpdate = userRepository.findById(input.getUserRegistrationId());
                        userUpdate.ifPresent(chassisEdit::setModificationUser);

                        chassisRepository.save(chassisEdit);
                    }
                }
            }
        }


        String containerNumber = containerRepository.findContainerNumberById(containerId);

        Optional<BusinessUnit> optionalBusinessUnit = businessUnitRepository.findOneById(businessUnitId);
        String businessUnitAlias = null;
        if (optionalBusinessUnit.isPresent()) {
            businessUnitAlias = optionalBusinessUnit.get().getBusinesUnitAlias();
        }


        Map<String, Object> resultRuleGeneralGetOut = systemRuleRepository.ruleGeneralGetOut(
                businessUnitAlias,
                "sd1_general_rule_by_business_unit",
                "[\"restriction_truck_departure_by_inspection\"]");

        String rAction = resultRuleGeneralGetOut.get("r_action").toString();


        if ("true".equalsIgnoreCase(rAction) && response.getResultMessage() == null) {
            String statusInspectionBoxPending = "";
            Integer eirGateIn;
            Integer catContainerStatus;
            String statusInspectionBox = null;
            Integer catChassisStatus;
            if (containerId != null
                    && (!Objects.equals(containerId, containerNoCntId) && !containerId.equals(containerNotApplicableId))
                    && Objects.equals(catMovementId, isGateOut)) {

                statusInspectionBoxPending = catalogRepository.findByAlias("sd1_statusinspectiongral_pending").getDescription();
                eirGateIn = sdgStockEmptyRepository.findGateInEirByContainerIdAndEirId(containerId, input.getEirId());

                if (eirGateIn == null) {
                    Optional<Integer> optionalEirGateIn = sdgStockEmptyRepository.findGateInEirByContainerIdAndSubBusinessUnitId(containerId, subBusinessUnitId);
                    if (optionalEirGateIn.isPresent()) {
                        eirGateIn = optionalEirGateIn.get();
                    }
                }
                catContainerStatus = eirRepository.fn_GetEquipmentConditionID(eirGateIn, isContainer, "", "GRAL");

                Optional<Catalog> catContainerStatusOptional = catalogRepository.findById(Optional.ofNullable(catContainerStatus).orElse(0));
                if (catContainerStatusOptional.isPresent()) {
                    statusInspectionBox = catContainerStatusOptional.get().getLongDescription();
                }


                if (Objects.equals(catOriginId, isGoCustomDelivery)
                        && (!Objects.equals(catContainerStatus, isOperativeContainer) && !Objects.equals(catContainerStatus, isOperativeDirtyContainer))) {

                    response.setRespResult(3);
                    response.setResultMessage(messageLanguageService.getMessage("TRUCK_DEPARTURE", 8, input.getLanguageId(),
                            Map.of("{status}", catContainerStatus == null ? statusInspectionBoxPending : statusInspectionBox)));
                    return response;
                }

            }

            if (eirChassisId != null
                    && (Objects.equals(containerId, containerNoCntId) || Objects.equals(containerId, containerNotApplicableId))
                    && Objects.equals(catMovementId, isGateOut)
                    && response.getResultMessage() == null) {

                statusInspectionBoxPending = catalogRepository.findByAlias("sd1_chassis_structurecondition_nc").getDescription();

                if (chassisId == null) {
                    chassisId = eirRepository.findChassisIdByEirIdAndEirChassisId(input.getEirId(), eirChassisId);
                }
                TbEirChassisDto tbEirChassisDto = eirRepository.findEirChassisGateInByChassisIdAndEirChassisId(chassisId, eirChassisId);
                Integer eirChassisGateinId = tbEirChassisDto.getEirId();
                Boolean flagChassisStayedGi = tbEirChassisDto.getFlagChassisStayed();
                Boolean flagOnSiteInspection = tbEirChassisDto.getFlagOnSiteInspection();

                catChassisStatus = eirRepository.fn_GetEquipmentConditionID(eirChassisGateinId, isChassis, "", "GRAL");
                statusInspectionBox = catalogRepository.findById(catChassisStatus).get().getLongDescription();

                if (flagChassisStayedGi && flagOnSiteInspection && Objects.equals(catOriginId, isGoCustomDelivery)
                        && !catChassisStatus.equals(isOperativeChassis)) {
                    response.setRespResult(3);
                    response.setResultMessage(messageLanguageService.getMessage("TRUCK_DEPARTURE", 9, input.getLanguageId(),
                            Map.of("{status}", catChassisStatus == null ? statusInspectionBoxPending : statusInspectionBox)));
                    return response;
                }

            }
        }

        Integer controlRevision = null;
        BigDecimal reportedWeight = null;
        Catalog catReportedWeight = null;
        Integer vesselProgrammingDetailId = null;
        String sealGif1 = null;
        String sealGif2 = null;
        String sealGif3 = null;
        String sealGif4 = null;
        Boolean activo = null;
        Integer catOriginCreationId = null;
        Integer transportPlanningDetailFullId = null;

        LocalDateTime truckOutDate = null;
        if (response.getResultMessage() == null) {

            if (containerId != null &&
                    !Objects.equals(containerId, containerNotApplicableId) &&
                    Objects.equals(catMovementId, isGateOut)) {
                ContainerRestriction containerRestriction = containerRestrictionRepository.findContainerRestrictionByParameters(containerId, subBusinessUnitId, catEmptyFullId);
                Integer containerRestrictionId = null;
                if (containerRestriction != null) {
                    containerRestrictionId = containerRestriction.getId();
                }

                String restriction = "";
                if (containerRestriction != null
                        && containerRestriction.getContainerRestrictionDetails() != null
                        && !containerRestriction.getContainerRestrictionDetails().isEmpty()) {
                    restriction = containerRestriction.getContainerRestrictionDetails().stream()
                            .filter(ContainerRestrictionDetail::getActive)
                            .map(obj -> obj.getCatRestrictionReason().getDescription())
                            .collect(Collectors.joining(", "));


                }
                restriction = restriction.trim();


                if (Objects.equals(catEmptyFullId, isFullId) && containerRestrictionId != null) {
                    response.setRespResult(2);
                    response.setResultMessage(messageLanguageService.getMessage(PRC_FULL_GI_GO, 29, input.getLanguageId(),
                            Map.of(CNTX, containerNumber, RSTX, restriction)));
                    return response;
                }

                if (Objects.equals(catEmptyFullId, isEmpty)
                        && (input.getPassRestriction() == null || input.getPassRestriction() == 0)
                        && response.getResultMessage() == null) {

                    Integer eirGateinContainerId = sdgStockEmptyRepository.findEirGateInIdByEirGateOutId(input.getEirId());
                    FgisInspection fgisInspection = fgisInspectionRepository.findByEirGateInAndContainerId(eirGateinContainerId, containerId);
                    Integer fgisInspectionId = fgisInspection != null ? fgisInspection.getId() : null;
                    Boolean usdaApproved = fgisInspection != null ? fgisInspection.getUsdaApproved() : null;
                    EirDocumentCargoDetail eirDocumentCargoDetail = eirCargoDetailRepository.findByEirAndActiveTrue(input.getEirId());
                    Integer preallocationContainerId = null;
                    if (eirDocumentCargoDetail != null) {
                        List<ContainerPreassignment> containerPreassignmentList = containerPreassignmentRepository.findByCargoDocumentDetailIdAndCatOriginPreassignment(eirDocumentCargoDetail.getId(), isPreAllocation);
                        if (containerPreassignmentList != null && !containerPreassignmentList.isEmpty()) {

                            preallocationContainerId = containerPreassignmentList.getFirst().getId();
                        }
                    }

                    if (preallocationContainerId != null) {
                        if (Boolean.TRUE.equals(containerRestrictionId != null && fgisInspectionId != null && usdaApproved)
                                && (input.getPassRestriction() == null || input.getPassRestriction() == 0)) {

                            response.setRespResult(4);
                            response.setRestrictionContainerId(containerRestrictionId);
                            response.setResultMessage(messageLanguageService.getMessage(PRC_FULL_GI_GO, 33, input.getLanguageId(),
                                    Map.of(CNTX, containerNumber, RSTX, restriction, BOOKING_NUMBER, bookingNumber)));
                            return response;
                        }

                        if (containerRestrictionId != null && fgisInspectionId == null) {
                            response.setRespResult(4);
                            response.setRestrictionContainerId(containerRestrictionId);
                            response.setResultMessage(messageLanguageService.getMessage(PRC_FULL_GI_GO, 30, input.getLanguageId(),
                                    Map.of(CNTX, containerNumber, RSTX, restriction, BOOKING_NUMBER, bookingNumber)));
                            return response;

                        }

                        if (containerRestrictionId == null && fgisInspectionId != null && Boolean.TRUE.equals(usdaApproved)) {
                            response.setRespResult(6);
                            response.setRestrictionContainerId(containerRestrictionId);
                            response.setResultMessage(messageLanguageService.getMessage(PRC_FULL_GI_GO, 32, input.getLanguageId(),
                                    Map.of(CNTX, containerNumber, BOOKING_NUMBER, bookingNumber)));
                            return response;

                        }

                    } else {
                        if (containerRestrictionId == null && fgisInspectionId != null && Boolean.TRUE.equals(usdaApproved)) {
                            response.setRespResult(6);
                            response.setRestrictionContainerId(containerRestrictionId);
                            response.setResultMessage(messageLanguageService.getMessage(PRC_FULL_GI_GO, 31, input.getLanguageId(),
                                    Map.of(CNTX, containerNumber)));
                            return response;

                        }
                    }
                }
            }

            if (eirMultipleId != null && Objects.equals(catMovementId, isGateOut)
                    && (Objects.equals(catEmptyFullId, isFullId) || Objects.equals(catEmptyFullId, isEmpty))) {
                eirMultipleId = null;
            }

            List<Eir> eirList = new ArrayList<>();
            Eir gateOutEir = eirRepository.findOneById(input.getEirId());
            if (eirMultipleId == null) {
                truckOutDate = gateOutEir.getTruckDepartureDate();
                activo = gateOutEir.getActive();
                sealGif1 = gateOutEir.getSeal1();
                sealGif2 = gateOutEir.getSeal2();
                sealGif3 = gateOutEir.getSeal3();
                sealGif4 = gateOutEir.getSeal4();
                catOriginCreationId = gateOutEir.getCatCreationOrigin().getId();
                if (gateOutEir.getTransportPlanningDetailFull() != null) {
                    transportPlanningDetailFullId = gateOutEir.getTransportPlanningDetailFull().getId();
                }

                if (gateOutEir != null) {
                    reportedWeight = gateOutEir.getWeightGoods();
                }

                Catalog catIsMesassureWeight = null;
                Optional<Catalog> catIsMesassureWeightOptional = catalogRepository.findById(isMeasureWeightKg);
                if (catIsMesassureWeightOptional.isPresent()) {
                    catIsMesassureWeight = catIsMesassureWeightOptional.get();
                }

                catReportedWeight = gateOutEir.getCatMeasureWeight() == null ? catIsMesassureWeight : gateOutEir.getCatMeasureWeight();
                vesselProgrammingDetailId = gateOutEir.getVesselProgrammingDetail().getId();
                if (gateOutEir.getControlRevision() != null) {
                    controlRevision = Integer.valueOf(gateOutEir.getControlRevision());
                } else {
                    controlRevision = null;
                }


                if (Objects.equals(catMovementId, isGateOut) && Objects.equals(catEmptyFullId, isFullId)
                        && (!Objects.equals(gateOutEir.getContainer().getId(), containerNoCntId)
                        && !Objects.equals(gateOutEir.getContainer().getId(), containerNotApplicableId))) {

                    Eir gateInEir = eirRepository.findGateInEirByGateOutEirId(input.getEirId()).orElse(null);
                    if (gateInEir == null) {
                        response.setRespResult(2);
                        response.setResultMessage(messageLanguageService.getMessage(SALIDA_CAMION, 5, input.getLanguageId()));
                        return response;
                    } else if (!Objects.equals(gateInEir.getSeal1(), seal1)
                            || !Objects.equals(gateInEir.getSeal2(), seal2)
                            || !Objects.equals(gateInEir.getSeal3(), seal3)
                            || !Objects.equals(gateInEir.getSeal4(), seal4)) {


                        String seals1 = Stream.of(gateOutEir.getSeal1(), gateOutEir.getSeal2(), gateOutEir.getSeal3(), gateOutEir.getSeal4())
                                .filter(Objects::nonNull)
                                .collect(Collectors.joining(", "));

                        String seals2 = Stream.of(seal1, seal2, seal3, seal4)
                                .filter(Objects::nonNull)
                                .collect(Collectors.joining(", "));

                        response.setRespResult(2);
                        response.setResultMessage(messageLanguageService.getMessage(SALIDA_CAMION, 4, input.getLanguageId(),
                                Map.of("{sealx1}", seals1, "{sealx2}", seals2)));
                        return response;
                    }
                }

                Eir gateOutFullEir = eirRepository.findOneById(input.getEirId());
                if (response.getResultMessage() == null) {

                    if (Boolean.TRUE.equals(gateOutFullEir.getActive()) && truckOutDate == null
                            && (!Objects.equals(gateOutFullEir.getCatCreationOrigin().getId(), isEirManual)
                            || (Objects.equals(gateOutFullEir.getCatCreationOrigin().getId(), isEirManual) && input.getTruckOutDate() == null))) {

                        if (input.getTruckOutDate() != null) {
                            if (gateOutFullEir.getTruckArrivalDate().isAfter(input.getTruckOutDate())) {
                                response.setRespResult(2);
                                response.setResultMessage(messageLanguageService.getMessage("PRC_EIR_MANUAL", 6, input.getLanguageId()));
                            } else {
                                truckOutDateZh = input.getTruckOutDate();
                            }
                        } else {
                            truckOutDateZh = LocalDateTime.now();
                        }

                        Eir eir = eirRepository.findOneById(input.getEirId());

                        eir.setTruckDepartureDate(truckOutDateZh);

                        eir.setTruckDepartureUser(userRegistration);
                        eir.setTraceEir("graltrkdeparture1");
                        eir.setModificationDate(LocalDateTime.now());
                        if (Objects.equals(catMovementId, isGateOut)) {
                            eir.setSeal1(seal1);
                            eir.setSeal1(seal2);
                            eir.setSeal1(seal3);
                            eir.setSeal1(seal4);
                        }

                        eir.setObservation(remarks);
                        if (Objects.equals(catMovementId, isGateOut) && Objects.equals(catEmptyFullId, isFullId)) {
                            eir.setControlAssignmentLight((short) 3);
                        }

                        if (Objects.equals(catMovementId, isGateIn) && (Objects.equals(controlRevision, 2) || Boolean.TRUE.equals(input.getReject()))) {
                            eir.setActive(false);
                        }

                        if (Objects.equals(catMovementId, isGateIn) &&
                                Objects.equals(controlRevision, 0) && Boolean.TRUE.equals(input.getReject())) {
                            eir.setControlRevision((short) 2);
                        }

                        if (Objects.equals(catMovementId, isGateOut) && input.getTemperature() != null) {
                            eir.setTemperature(input.getTemperature());
                        }

                        if (Objects.equals(catMovementId, isGateOut) && input.getTemperature() != null) {
                            Catalog catTemperatur = null;
                            Optional<Catalog> catTemperaturOptional = catalogRepository.findById(input.getTemperatureUnit());
                            if (catTemperaturOptional.isPresent()) {
                                catTemperatur = catTemperaturOptional.get();
                            }
                            eir.setCatTemperatureMeasure(catTemperatur);

                        }

                        eirRepository.save(eir);

                        List<Attachment> listAttachmentId = new ArrayList<>();
                        if (input.getPhotos() != null) {

                            JSONArray jsonArray = new JSONArray(input.getPhotos());
                            for (int i = 0; i < jsonArray.length(); i++) {
                                JSONObject jsonObject = jsonArray.getJSONObject(i);

                                String name = jsonObject.getString("nombre");
                                String weight = jsonObject.getString("peso");
                                String format = jsonObject.getString("formato");
                                String path = jsonObject.getString("ubicacion");
                                String url = jsonObject.getString("url");
                                String id = Optional.ofNullable(jsonObject.getString("id")).orElse(null);
                                int attachmentType = jsonObject.getInt("tipoAdjunto");
                                Optional<Catalog> catAttachmentTypeOptional = catalogRepository.findById(attachmentType);

                                Catalog catAttachmentType = null;
                                if (catAttachmentTypeOptional.isPresent()) {
                                    catAttachmentType = catAttachmentTypeOptional.get();
                                }
                                Attachment attachment = new Attachment();
                                attachment.setName(name);
                                attachment.setWeight(Integer.valueOf(weight));
                                attachment.setFormat(format);
                                attachment.setLocation(path);

                                attachment.setUrl(url);
                                attachment.setId1(id);
                                attachment.setCatAttachmentType(catAttachmentType);
                                attachment = attachmentRepository.save(attachment);
                                listAttachmentId.add(attachment);
                            }
                        }
                        if (!listAttachmentId.isEmpty()) {
                            if (Objects.equals(catEmptyFullId, isFullId)) {
                                listAttachmentId.forEach(attachment -> {
                                    SdfEirPhoto sdfEirPhoto = new SdfEirPhoto();
                                    sdfEirPhoto.setAttached(attachment);
                                    sdfEirPhoto.setRegistrationDate(LocalDateTime.now());
                                    sdfEirPhoto.setUserRegistration(userRegistration);
                                    sdfEirPhoto.setActive(true);
                                    sdfEirPhoto.setEir(eir);
                                    sdfEirPhotoRepository.save(sdfEirPhoto);
                                });

                            }
                            if (Objects.equals(catEmptyFullId, isEmpty)) {

                                listAttachmentId.forEach(attachment -> {
                                    SdsEirPhoto sdsEirPhoto = new SdsEirPhoto();
                                    sdsEirPhoto.setAttachment(attachment);
                                    sdsEirPhoto.setRegistrationDate(LocalDateTime.now());
                                    sdsEirPhoto.setUserRegistration(userRegistration);
                                    sdsEirPhoto.setActive(true);
                                    sdsEirPhoto.setEir(eir);
                                    sdsEirPhotoRepository.save(sdsEirPhoto);
                                });
                            }
                        }

                        if (Objects.equals(catMovementId, isGateIn) && (Objects.equals(controlRevision, 2)
                                || Boolean.TRUE.equals(input.getReject()))) {


                            if (eirChassisAuxId != null) {
                                Integer bookingChassisAuxId = chassisDocumentDetailRepository.findDocumentChassisBookingIdByDocumentChassisDetailId(documentChassisDetailAuxId);

                                Optional<ChassisDocumentDetail> chassisDocumentDetailOptional = chassisDocumentDetailRepository.findById(documentChassisDetailAuxId);
                                ChassisDocumentDetail chassisDocumentDetail = null;
                                if (chassisDocumentDetailOptional.isPresent()) {
                                    chassisDocumentDetail = chassisDocumentDetailOptional.get();
                                }

                                Optional<Catalog> catStatuChassisOptional = catalogRepository.findById(isDocChassisPending);
                                Catalog catStatuChassis = null;
                                if (catStatuChassisOptional.isPresent()) {
                                    catStatuChassis = catStatuChassisOptional.get();
                                }
                                chassisDocumentDetail.setCatStatusChassis(catStatuChassis);
                                chassisDocumentDetail.setChassis(null);
                                chassisDocumentDetail.setModificationUser(userRegistration);
                                chassisDocumentDetail.setModificationDate(LocalDateTime.now());
                                chassisDocumentDetail.setTraceChassisDocDetail(TRUCKDEPA_RECH_CHGO_1);
                                chassisDocumentDetailRepository.save(chassisDocumentDetail);

                                Integer qchaAttendedAux = chassisDocumentDetailRepository.countDocumentChassisDetailByBookingChassisId(bookingChassisAuxId);
                                Optional<ChassisBookingDocument> chassisBookingDocumentOptional = chassisBookingDocumentRepository.findById(bookingChassisAuxId);
                                ChassisBookingDocument chassisBookingDocument = null;
                                if (chassisBookingDocumentOptional.isPresent()) {
                                    chassisBookingDocument = chassisBookingDocumentOptional.get();
                                }

                                chassisBookingDocument.setAttendedQuantity(qchaAttendedAux == null ? 0 : qchaAttendedAux);
                                chassisBookingDocument.setModificationUser(userRegistration);
                                chassisBookingDocument.setModificationDate(LocalDateTime.now());
                                chassisBookingDocumentRepository.save(chassisBookingDocument);

                                Eir eirEdit = eirRepository.findByEirIdAndActive(eirAuxId);
                                eirEdit.setActive(false);
                                if (controlRevision != null) {
                                    eirEdit.setControlRevision((short) (Objects.equals(controlRevision, 0) ? 4 : controlRevision));
                                }

                                eirEdit.setTraceEir(TRUCKDEPA_RECH_CHGO_1);
                                eirEdit.setModificationUser(userRegistration);
                                eirEdit.setModificationDate(LocalDateTime.now());
                                eirEdit.setDeleteDate(LocalDateTime.now());
                                eirEdit.setDeleteUser(userRegistration);
                                eirRepository.save(eirEdit);


                                EirChassis eirChassis = eirChassisRepository.findEirChassisByIdAndActive(eirChassisAuxId);
                                eirChassis.setActive(false);
                                eirChassis.setModificationUser(userRegistration);
                                eirChassis.setModificationDate(LocalDateTime.now());
                                eirChassis.setTraceEirChassis(TRUCKDEPA_RECH_CHGO_1);
                                eirChassisRepository.save(eirChassis);
                            }

                            Optional<Eir> eirOptional = eirRepository.findById(input.getEirId());
                            if (eirOptional.isPresent()) {
                                Eir eirEdit = eirOptional.get();
                                eirEdit.setActive(false);
                                eirEdit.setControlRevision((short) (controlRevision == 0 ? 4 : controlRevision));
                                eirEdit.setDeleteUser(userRegistration);
                                eirEdit.setDeleteDate(LocalDateTime.now());
                                eirRepository.save(eirEdit);
                            }

                            if (eirChassisId != null) {
                                Optional<EirChassis> eirChassisOptional = eirChassisRepository.findById(eirChassisId);
                                if (eirChassisOptional.isPresent()) {
                                    EirChassis eirChassis = eirChassisOptional.get();
                                    eirChassis.setActive(false);
                                    eirChassis.setModificationUser(userRegistration);
                                    eirChassis.setModificationDate(LocalDateTime.now());
                                    eirChassis.setTraceEirChassis("graltruckdepa_rech1");
                                    eirChassisRepository.save(eirChassis);
                                }

                            }
                        }

                        if (Objects.equals(catEmptyFullId, isFullId)) {
                            if (Objects.equals(catMovementId, isGateIn) && Boolean.FALSE.equals(input.getReject())) {
                                CargoDocumentDetail cargoDocumentDetail = cargoDocumentDetailRepository.findByEirId(input.getEirId());
                                cargoDocumentDetail.setReceivedWeight(reportedWeight);
                                cargoDocumentDetail.setCatReceivedWeightMeasure(catReportedWeight);
                                cargoDocumentDetail.setReceivedQuantity(BigDecimal.valueOf(1));
                                cargoDocumentDetail.setReceivedVolume(BigDecimal.valueOf(0));
                                cargoDocumentDetail.setBalanceQuantity(BigDecimal.valueOf(1));
                                cargoDocumentDetail.setBalanceWeight(reportedWeight);
                                cargoDocumentDetail.setModificationUser(userRegistration);
                                cargoDocumentDetail.setModificationDate(LocalDateTime.now());
                                cargoDocumentDetail.setTraceCargoDocumentDetail("ggi_trkdeparture2");

                                cargoDocumentDetailRepository.save(cargoDocumentDetail);


                                VesselProgrammingContainer vesselProgrammingContainer =
                                        //vesselProgrammingContainerRepository.findByIdAndContainerIdAndActiveTrue(vesselProgrammingDetailId, containerId);
                                        vesselProgrammingContainerRepository.findByVesselProgrammingDetailIdAndContainerIdAndActive(vesselProgrammingDetailId, containerId, true).orElseThrow(() -> {
                                            throw new EntityNotFoundException("There's no vessel programming container with the given id and container id");
                                        });
                                vesselProgrammingContainer.setReceivedQuantity(0);
                                vesselProgrammingContainer.setReceivedWeight(reportedWeight);
                                vesselProgrammingContainer.setCatReceivedWeightMeasure(catReportedWeight);
                                vesselProgrammingContainer.setReceivedSeal1(sealGif1);
                                vesselProgrammingContainer.setReceivedSeal2(sealGif2);
                                vesselProgrammingContainer.setReceivedSeal3(sealGif3);
                                vesselProgrammingContainer.setReceivedSeal4(sealGif4);
                                vesselProgrammingContainer.setModificationUser(userRegistration);
                                vesselProgrammingContainer.setModificationDate(LocalDateTime.now());
                                vesselProgrammingContainer.setTraceProgVesCnt("ggi_trkdeparture2");
                                vesselProgrammingContainerRepository.save(vesselProgrammingContainer);


                            }

                            if (Objects.equals(catMovementId, isGateOut)) {
                                CargoDocumentDetail cargoDocumentDetail = cargoDocumentDetailRepository.findByEirId(input.getEirId());
                                cargoDocumentDetail.setDispatchedQuantity(BigDecimal.valueOf(1));
                                cargoDocumentDetail.setDispatchedWeight(reportedWeight);
                                cargoDocumentDetail.setDispatchedVolume(BigDecimal.valueOf(0));
                                cargoDocumentDetail.setBalanceQuantity(BigDecimal.valueOf(0));

                                if (cargoDocumentDetail.getBalanceWeight() != null) {
                                    if (cargoDocumentDetail.getBalanceWeight().subtract(reportedWeight).compareTo(BigDecimal.ZERO) < 0) {
                                        cargoDocumentDetail.setBalanceWeight(BigDecimal.valueOf(0));
                                    } else {
                                        cargoDocumentDetail.setBalanceWeight(cargoDocumentDetail.getBalanceWeight().subtract(reportedWeight));
                                    }
                                } else {
                                    cargoDocumentDetail.setBalanceWeight(BigDecimal.valueOf(0));
                                }
                                cargoDocumentDetail.setBalanceVolume(BigDecimal.valueOf(0));

                                cargoDocumentDetail.setCatDispatchedWeightMeasure(catReportedWeight);

                                cargoDocumentDetail.setModificationUser(userRegistration);
                                cargoDocumentDetail.setModificationDate(LocalDateTime.now());
                                cargoDocumentDetail.setTraceCargoDocumentDetail("ggo_trkdeparture3");

                                cargoDocumentDetailRepository.save(cargoDocumentDetail);


                                VesselProgrammingContainer vesselProgrammingContainer =
                                        vesselProgrammingContainerRepository.findByVesselProgrammingDetailIdAndContainerIdAndActive(vesselProgrammingDetailId, containerId, true).orElseThrow(() -> {
                                            throw new EntityNotFoundException("There's no vessel programming container with the given id and container id");
                                        });

                                vesselProgrammingContainer.setReceivedSeal1(seal1);
                                vesselProgrammingContainer.setReceivedSeal2(seal2);
                                vesselProgrammingContainer.setReceivedSeal3(seal3);
                                vesselProgrammingContainer.setReceivedSeal4(seal4);
                                vesselProgrammingContainer.setModificationUser(userRegistration);
                                vesselProgrammingContainer.setModificationDate(LocalDateTime.now());
                                vesselProgrammingContainer.setTraceProgVesCnt("ggo_trkdeparture3");
                                vesselProgrammingContainer.setDispatchedTemperatureC(input.getTemperature());
                                Optional<Catalog> catDispatchedTemperatureMeasureOptional = catalogRepository.findById(Optional.ofNullable(input.getTemperatureUnit()).orElse(0));
                                Catalog catDispatchedTemperatureMeasure = null;
                                if (catDispatchedTemperatureMeasureOptional.isPresent()) {
                                    catDispatchedTemperatureMeasure = catDispatchedTemperatureMeasureOptional.get();
                                }
                                vesselProgrammingContainer.setCatDispatchedTemperatureMeasure(catDispatchedTemperatureMeasure);
                                vesselProgrammingContainerRepository.save(vesselProgrammingContainer);

                            }

                            if (transportPlanningDetailFullId != null) {
                                TransportPlanningDetail transportPlanningDetail = transportPlanningDetailRepository.findOneById(transportPlanningDetailFullId);
                                if (Objects.equals(catMovementId, isGateIn) && Boolean.TRUE.equals(input.getReject())) {
                                    Optional<Catalog> catIsPlanningPendingOptional = catalogRepository.findById(isPlanningPending);
                                    Catalog catIsPlanningPending = null;
                                    if (catIsPlanningPendingOptional.isPresent()) {
                                        catIsPlanningPending = catIsPlanningPendingOptional.get();
                                    }
                                    transportPlanningDetail.setCatTrkPlanningState(catIsPlanningPending);
                                } else {
                                    Optional<Catalog> catIsPlanningDoneOptional = catalogRepository.findById(isPlanningDone);
                                    Catalog catIsPlanningDone = null;
                                    if (catIsPlanningDoneOptional.isPresent()) {
                                        catIsPlanningDone = catIsPlanningDoneOptional.get();
                                    }
                                    transportPlanningDetail.setCatTrkPlanningState(catIsPlanningDone);
                                }
                                transportPlanningDetail.setModificationUser(userRegistration);
                                transportPlanningDetail.setModificationDate(LocalDateTime.now());
                                transportPlanningDetailRepository.save(transportPlanningDetail);

                            }


                        }

                        if (documentChassisDetailId != null) {
                            Optional<ChassisDocumentDetail> chassisDocumentDetailOptional = chassisDocumentDetailRepository.findById(documentChassisDetailId);
                            ChassisDocumentDetail chassisDocumentDetail = null;
                            if (chassisDocumentDetailOptional.isPresent()) {
                                chassisDocumentDetail = chassisDocumentDetailOptional.get();
                                if (Objects.equals(catMovementId, isGateIn) && Boolean.TRUE.equals(input.getReject())) {
                                    Optional<Catalog> catIsDocChassisPendingOptional = catalogRepository.findById(isDocChassisPending);
                                    Catalog catIsDocChassisPending = null;
                                    if (catIsDocChassisPendingOptional.isPresent()) {
                                        catIsDocChassisPending = catIsDocChassisPendingOptional.get();
                                    }
                                    chassisDocumentDetail.setCatStatusChassis(catIsDocChassisPending);
                                } else {
                                    Optional<Catalog> catIsDocChassisCompletedOptional = catalogRepository.findById(isDocChassisCompleted);
                                    Catalog catIsDocChassisCompleted = null;
                                    if (catIsDocChassisCompletedOptional.isPresent()) {
                                        catIsDocChassisCompleted = catIsDocChassisCompletedOptional.get();
                                    }
                                    chassisDocumentDetail.setCatStatusChassis(catIsDocChassisCompleted);
                                }
                                chassisDocumentDetail.setModificationUser(userRegistration);
                                chassisDocumentDetail.setModificationDate(LocalDateTime.now());

                                if (Objects.equals(catMovementId, isGateIn) && Boolean.TRUE.equals(input.getReject())) {
                                    chassisDocumentDetail.setTraceChassisDocDetail("graltruckdepa_rech1");
                                } else {
                                    chassisDocumentDetail.setTraceChassisDocDetail("graltrkdeparture6");
                                }
                                chassisDocumentDetailRepository.save(chassisDocumentDetail);
                            }

                        }

                        if (input.getNewTruckCompanyId() != null) {
                            Optional<Company> companyOptional = companyRepository.findById(input.getNewTruckCompanyId());
                            Company company = null;
                            if (companyOptional.isPresent()) {
                                company = companyOptional.get();
                            }
                            Eir eirEdit = null;
                            Optional<Eir> eirEditOptional = eirRepository.findById(input.getEirId());
                            if (eirEditOptional.isPresent()) {
                                eirEdit = eirEditOptional.get();
                                eirEdit.setTransportCompany(company);
                                eirEdit.setModificationUser(userRegistration);
                                eirEdit.setModificationDate(LocalDateTime.now());
                                eirEdit.setTraceEir("upd_truck_truck_dep");
                                eirRepository.save(eirEdit);
                            }
                        }
                        response.setRespResult(1);

                    }
                } else {
                    if (Boolean.FALSE.equals(activo)) {
                        response.setResultMessage(messageLanguageService.getMessage(SALIDA_CAMION, 1, input.getLanguageId(),
                                Map.of(EIRX, input.getEirId().toString())));
                    }
                    if (response.getResultMessage() == null && Objects.equals(catOriginCreationId, isEirManual) && input.getTruckOutDate() == null) {
                        response.setResultMessage(messageLanguageService.getMessage(SALIDA_CAMION, 3, input.getLanguageId()));
                    }
                    if (response.getResultMessage() == null && truckOutDate != null) {

                        response.setResultMessage(messageLanguageService.getMessage(SALIDA_CAMION, 2, input.getLanguageId(),
                                Map.of(EIRX, input.getEirId().toString(), "{FECHAX}", gateOutFullEir.getTruckDepartureDate().toString())));
                    }
                    response.setRespResult(2);
                    return response;
                }

            } else {
                if (input.getTruckOutDate() != null) {
                    truckOutDateZh = input.getTruckOutDate();
                } else {
                    truckOutDateZh = LocalDateTime.now();
                }

                eirList = eirRepository.findByEirMultipleId(eirMultipleId);
                List<Eir> eirTruckDepartureDateNotNull = eirList.stream().filter(eir -> eir.getTruckDepartureDate() != null).toList();
                if (eirList.stream().filter(eir -> eir.getTruckDepartureDate() == null).toList().isEmpty() && !eirTruckDepartureDateNotNull.isEmpty()) {


                    eirAuxId = eirTruckDepartureDateNotNull.getFirst().getId();
                    truckOutDate = eirTruckDepartureDateNotNull.getFirst().getTruckDepartureDate();

                    response.setResultMessage(messageLanguageService.getMessage(SALIDA_CAMION, 2, input.getLanguageId(),
                            Map.of(EIRX, eirAuxId.toString(), "{FECHAX}", truckOutDate.toString())));
                    response.setRespResult(2);
                    return response;


                } else {
                    LocalDateTime finalTruckOutDateZh = truckOutDateZh;
                    eirList.forEach(eir -> {
                        eir.setTruckDepartureDate(finalTruckOutDateZh);
                        eir.setTruckDepartureUser(userRegistration);
                        eir.setTraceEir("graltrkdeparture4");
                        eir.setModificationDate(LocalDateTime.now());
                        eir.setModificationUser(userRegistration);
                        eir.setObservation(remarks);

                        if (Objects.equals(catMovementId, isGateIn) && Boolean.TRUE.equals(input.getReject())) {
                            eir.setActive(false);
                        }
                        eirRepository.save(eir);

                    });


                    if (Objects.equals(catMovementId, isGateIn)
                            && (!eirList.stream().filter(eir -> Integer.valueOf(2).equals(eir.getControlRevision())
                    ).toList().isEmpty() || input.getReject())) {
                        if (eirChassisAuxId != null) {
                            ChassisDocumentDetail chassisDocumentDetail = chassisDocumentDetailRepository.findById(documentChassisDetailAuxId).get();
                            Integer bookingChassisAuxId = chassisDocumentDetail.getChassisBookingDocument().getId();
                            Catalog catIsDocChassisPending = catalogRepository.findById(isDocChassisPending).get();
                            chassisDocumentDetail.setCatStatusChassis(catIsDocChassisPending);
                            chassisDocumentDetail.setChassis(null);
                            chassisDocumentDetail.setModificationUser(userRegistration);
                            chassisDocumentDetail.setModificationDate(LocalDateTime.now());
                            chassisDocumentDetail.setTraceChassisDocDetail(TRUCKDEPA_RECH_CHGO_2);
                            chassisDocumentDetailRepository.save(chassisDocumentDetail);


                            Integer qchaAttendedAux =
                                    chassisDocumentDetailRepository.countDocumentChassisDetailByBookingChassisId(bookingChassisAuxId);

                            ChassisBookingDocument chassisBookingDocument = chassisBookingDocumentRepository.findById(bookingChassisAuxId).get();
                            chassisBookingDocument.setAttendedQuantity(qchaAttendedAux);
                            chassisBookingDocument.setModificationUser(userRegistration);
                            chassisBookingDocument.setModificationDate(LocalDateTime.now());

                            chassisBookingDocumentRepository.save(chassisBookingDocument);

                            Eir eirEdit = eirRepository.findByEirIdAndActive(eirAuxId);
                            eirEdit.setActive(false);
                            if (controlRevision != null && controlRevision == 4) {
                                eirEdit.setControlRevision((short) 4);
                            }
                            eirEdit.setDeleteUser(userRegistration);
                            eirEdit.setDeleteDate(LocalDateTime.now());
                            eirEdit.setTraceEir(TRUCKDEPA_RECH_CHGO_2);
                            eirEdit.setModificationUser(userRegistration);
                            eirEdit.setModificationDate(LocalDateTime.now());
                            eirRepository.save(eirEdit);

                            EirChassis eirChassis = eirChassisRepository.findEirChassisByIdAndActive(eirChassisAuxId);
                            eirChassis.setActive(false);
                            eirChassis.setModificationUser(userRegistration);
                            eirChassis.setModificationDate(LocalDateTime.now());
                            eirChassis.setTraceEirChassis(TRUCKDEPA_RECH_CHGO_2);
                            eirChassisRepository.save(eirChassis);

                        }

                        eirList.forEach(eir -> {
                            eir.setTraceEir(GRALTRUCKDEPA_RECH_2);
                            eir.setActive(false);
                            if (eir.getControlRevision() != null && eir.getControlRevision() == 0) {
                                eir.setControlRevision((short) 4);
                            }
                            eir.setDeleteUser(userRegistration);
                            eir.setDeleteDate(LocalDateTime.now());
                            eirRepository.save(eir);
                        });

                        if (eirChassisId != null) {
                            EirChassis eirChassis = eirChassisRepository.findEirChassisByIdAndActive(eirChassisId);
                            eirChassis.setActive(false);
                            eirChassis.setModificationUser(userRegistration);
                            eirChassis.setModificationDate(LocalDateTime.now());
                            eirChassis.setTraceEirChassis(GRALTRUCKDEPA_RECH_2);
                            eirChassisRepository.save(eirChassis);
                        }

                    }

                    if (Objects.equals(catMovementId, isGateIn)
                            && Objects.equals(catEmptyFullId, isFullId) && Boolean.FALSE.equals(input.getReject())) {

                        List<Integer> eirIdsList = new ArrayList<>();
                        eirList.forEach(eir ->
                                eirIdsList.add(eir.getId())
                        );

                        List<EirDocumentCargoDetail> eirDocumentCargoDetailList = eirDocumentCargoDetailRepository.findManyByEirId(eirIdsList);

                        eirDocumentCargoDetailList.forEach(eirDocumentCargoDetail -> {
                            CargoDocumentDetail cargoDocumentDetail = eirDocumentCargoDetail.getCargoDocumentDetail();
                            cargoDocumentDetail.setReceivedQuantity(BigDecimal.valueOf(1));
                            cargoDocumentDetail.setReceivedWeight(eirDocumentCargoDetail.getEir().getWeightGoods());
                            cargoDocumentDetail.setCatWeightMeasureUnit(eirDocumentCargoDetail.getEir().getCatMeasureWeight());
                            cargoDocumentDetail.setReceivedVolume(BigDecimal.valueOf(1));
                            cargoDocumentDetail.setBalanceQuantity(BigDecimal.valueOf(1));
                            cargoDocumentDetail.setBalanceWeight(eirDocumentCargoDetail.getEir().getWeightGoods());
                            cargoDocumentDetail.setBalanceVolume(BigDecimal.valueOf(0));
                            cargoDocumentDetail.setModificationUser(userRegistration);
                            cargoDocumentDetail.setModificationDate(LocalDateTime.now());
                            cargoDocumentDetail.setTraceCargoDocumentDetail("ggi_trkdeparture5");
                            cargoDocumentDetailRepository.save(cargoDocumentDetail);
                        });


                        List<VesselProgrammingContainer> vesselProgrammingContainers = vesselProgrammingContainerRepository.findByEirIdList(eirIdsList);
                        List<Eir> finalEirList = eirList;
                        vesselProgrammingContainers.forEach(vesselProgrammingContainer -> {
                            vesselProgrammingContainer.setReceivedQuantity(0);
                            Eir eirSearch = null;
                            for (Eir eir : finalEirList) {
                                if (Objects.equals(eir.getVesselProgrammingDetail().getId(), vesselProgrammingContainer.getVesselProgrammingDetail().getId())) {
                                    eirSearch = eir;
                                }
                            }
                            if (eirSearch != null) {
                                vesselProgrammingContainer.setReceivedWeight(eirSearch.getWeightGoods());
                                vesselProgrammingContainer.setCatReceivedWeightMeasure(eirSearch.getCatMeasureWeight());
                                vesselProgrammingContainer.setManifestedSeal1(eirSearch.getSeal1());
                                vesselProgrammingContainer.setManifestedSeal2(eirSearch.getSeal2());
                                vesselProgrammingContainer.setManifestedSeal3(eirSearch.getSeal3());
                                vesselProgrammingContainer.setManifestedSeal4(eirSearch.getSeal4());
                                vesselProgrammingContainer.setModificationUser(userRegistration);
                                vesselProgrammingContainer.setModificationDate(LocalDateTime.now());
                                vesselProgrammingContainer.setTraceProgVesCnt("ggi_trkdeparture5");
                            }
                            vesselProgrammingContainerRepository.save(vesselProgrammingContainer);


                        });
                    }

                    if (Objects.equals(catEmptyFullId, isFullId)) {
                        for (Eir eir : eirList) {
                            TransportPlanningDetail transportPlanningDetail = eir.getTransportPlanningDetailFull();

                            if (Objects.equals(catMovementId, isGateIn) && Boolean.TRUE.equals(input.getReject())) {
                                Catalog catIsPlanningPending = catalogRepository.findById(isPlanningPending).get();
                                transportPlanningDetail.setCatTrkPlanningState(catIsPlanningPending);
                            } else {
                                Catalog catIsPlanningDone = catalogRepository.findById(isPlanningDone).get();
                                transportPlanningDetail.setCatTrkPlanningState(catIsPlanningDone);
                            }
                            transportPlanningDetail.setModificationUser(userRegistration);
                            transportPlanningDetail.setModificationDate(LocalDateTime.now());
                            transportPlanningDetailRepository.save(transportPlanningDetail);

                        }
                    }

                    if (documentChassisDetailId != null) {
                        Optional<ChassisDocumentDetail> optionalChassisDocumentDetail = chassisDocumentDetailRepository.findById(documentChassisDetailId);
                        if (optionalChassisDocumentDetail.isPresent()) {
                            ChassisDocumentDetail chassisDocumentDetail = optionalChassisDocumentDetail.get();
                            if (Objects.equals(catMovementId, isGateIn) && Boolean.TRUE.equals(input.getReject())) {
                                Optional<Catalog> optionalCatIsDocChassisPending = catalogRepository.findById(isDocChassisPending);
                                if (optionalCatIsDocChassisPending.isPresent()) {
                                    Catalog catIsDocChassisPending = optionalCatIsDocChassisPending.get();
                                    chassisDocumentDetail.setCatStatusChassis(catIsDocChassisPending);
                                }
                                chassisDocumentDetail.setTraceChassisDocDetail(GRALTRUCKDEPA_RECH_2);
                            } else {
                                Catalog catIsDocChassisCompleted = catalogRepository.findById(isDocChassisCompleted).get();
                                chassisDocumentDetail.setCatStatusChassis(catIsDocChassisCompleted);
                                chassisDocumentDetail.setTraceChassisDocDetail("graltrkdeparture7");
                            }
                            chassisDocumentDetail.setModificationUser(userRegistration);
                            chassisDocumentDetail.setModificationDate(LocalDateTime.now());
                            chassisDocumentDetailRepository.save(chassisDocumentDetail);
                        }
                    }

                    response.setRespResult(1);


                }
            }

            Eir eirEdit = eirRepository.findOneById(input.getEirId());
            if (eirEdit != null) {
                eirEdit.setConfirmAddContainerNotMatch(input.getConfirmAddContainerNotMatch());
                eirRepository.save(eirEdit);
            }

            String pContainerNumber;
            String pEquipmentNotApplicable = "NOT APPLICA";
            if (Objects.equals(catEmptyFullId, isEmpty) && Objects.equals(catMovementId, isGateIn) && response.getRespResult() != null
                    && response.getRespResult() == 1) {
                if (eirMultipleId == null) {
                    Eir eir = eirRepository.findOneById(input.getEirId());
                    pContainerNumber = eir.getContainer() != null ? eir.getContainer().getContainerNumber() : null;
                    if (pContainerNumber != null && !Objects.equals(pContainerNumber, pEquipmentNotApplicable)) {
                        EirSendAppeir eirSendAppeir = new EirSendAppeir();
                        eirSendAppeir.setEir(eir);
                        eirSendAppeir.setFlagSend('0');
                        eirSendAppeir.setRegistrationDate(LocalDateTime.now());
                        eirSendAppeirRepository.save(eirSendAppeir);
                    }
                } else {

                    for (Eir eir : eirList.stream().filter(eir -> !Objects.equals(eir.getContainer().getContainerNumber(), pEquipmentNotApplicable)).toList()) {
                        EirSendAppeir eirSendAppeir = new EirSendAppeir();
                        eirSendAppeir.setEir(eir);
                        eirSendAppeir.setFlagSend('0');
                        eirSendAppeir.setRegistrationDate(LocalDateTime.now());
                        eirSendAppeirRepository.save(eirSendAppeir);
                    }
                }
            }


            if (containerId != null
                    && (!Objects.equals(containerId, containerNotApplicableId) && !Objects.equals(containerId, containerNoCntId))
                    && Objects.equals(catMovementId, isGateOut)) {
                List<Object> tRuleAps = eirRepository.gateGeneralAppointmentValidate(input.getSubBusinessUnitLocalId(), "gateout", null);

                if (tRuleAps.size() > 1 && tRuleAps.get(1) == "aps") {
                    Eir eir = eirRepository.findOneById(input.getEirId());
                    eir.setAppointmentFlagOndepupdcnt(false);
                    eirRepository.save(eir);
                }
            }

        }


        if (response != null && (Integer.valueOf(1)).equals(response.getRespResult())) {
            Map<String, Object> resultRuleGeneralGetOut2 = systemRuleRepository.ruleGeneralGetOut(
                    businessUnitAlias,
                    "sd1_general_rule_by_business_unit",
                    "[\"truck_departure_ticket_notification\"]");

            String rAction2 = resultRuleGeneralGetOut2.get("r_action").toString();
            Eir eir = eirRepository.findOneById(input.getEirId());
            if (Objects.equals(rAction2, "true")) {

                Catalog catEirNotificationPending = catalogRepository.findById(eirNotificationPending).get();
                EirNotification eirNotification = new EirNotification();
                eirNotification.setEir(eir);
                eirNotification.setStatus(catEirNotificationPending);
                eirNotification.setActive(true);
                eirNotification.setRegistrationUser(userRegistration);
                eirNotification.setRegistrationDate(LocalDateTime.now());
                eirNotificationRepository.save(eirNotification);
            }
        }
        return response;
    }


    private String formatString(String input) {
        return input == null ? "" : input.trim().toUpperCase();
    }


    public SdgTruckDepartureRegisterOutput truckDepartureRegisterProcess(SdgTruckDepartureRegisterInput.Root inputRoot) throws Exception {
        SdgTruckDepartureRegisterInput.Input input = inputRoot.getSdg().getInput();
        TruckDepartureRegisterService selfI = context.getBean(TruckDepartureRegisterService.class);
        Boolean checkInt = selfI.validateIntegrationWithyard(input);

        boolean flagRegister = false;
        boolean flagAssigment = false;

        Eir eir = eirRepository.findOneById(input.getEirId());

        if (eir != null && Objects.equals(eir.getCatMovement().getAlias(), Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS)) {
            flagAssigment = true;
            if (Boolean.TRUE.equals(checkInt)) {
                if (input.getContainerId() != null) {
                    String json = objectMapper.writeValueAsString(inputRoot);
                    SdgTruckDepartureRegisterBeforeYardInput.Root registerBeforeYardInput = objectMapper.readValue(json, SdgTruckDepartureRegisterBeforeYardInput.Root.class);
                    ResponseTruckDepartureRegisterBeforeYard responseTruckDepartureRegisterBeforeYard = truckDepartureRegisterBeforeYardService.registerBeforeYard(registerBeforeYardInput);

                    if (responseTruckDepartureRegisterBeforeYard.getRespResult() == 1) {
                        flagRegister = true;
                    }
                } else {
                    flagRegister = true;
                }
            } else {
                flagRegister = true;
            }
        } else {
            flagRegister = true;
        }

        boolean rptaAssigment = false;

        if (flagRegister && flagAssigment) {
            TruckDepartureRegisterService self = context.getBean(TruckDepartureRegisterService.class);
            rptaAssigment = self.implementAssigmentGateOut(inputRoot);

            if (!rptaAssigment) {
                throw new PersistenceException();
            }

        } else {
            rptaAssigment = true;
        }


        if (flagRegister && rptaAssigment) {
            TruckDepartureRegisterService self = context.getBean(TruckDepartureRegisterService.class);
            return self.register(inputRoot);
        } else {
            throw new PersistenceException();
        }
    }

    @Transactional
    public Boolean validateIntegrationWithyard(SdgTruckDepartureRegisterInput.Input input) {
        return eirRepository.validateYardIntegration(input.getSubBusinessUnitLocalId(), null, null);
    }

    @Transactional
    public boolean implementAssigmentGateOut(SdgTruckDepartureRegisterInput.Root inputRoot) throws PersistenceException, IOException {
        SdgTruckDepartureRegisterInput.Input input = inputRoot.getSdg().getInput();
        Integer isForGateOutAssignation = input.getIsForGateOutAssignation();

        Map<String, Object> responseAssignGout;
        if (isForGateOutAssignation == 1) {
            responseAssignGout = gateoutGeneralAssignmentRegister(inputRoot, true);
            return responseAssignGout != null && responseAssignGout.get("result_state").equals(1);
        } else {
            return true;
        }
    }


    public Map<String, Object> gateoutGeneralAssignmentRegister(SdgTruckDepartureRegisterInput.Root inputRoot, boolean avoidYard) throws PersistenceException, IOException {
        SdgTruckDepartureRegisterInput.Input input = inputRoot.getSdg().getInput();
        Map<String, Object> responseAssignGout = eirRepository.gateoutGeneralAssignmentRegisterV2(input.getSubBusinessUnitLocalId(), input.getEirId(),
                input.getContainerId(), input.getChassisId(), input.getDocumentoCargaDetalleId(), input.getPlanningDetailId(),
                input.getPhotos(), input.getSeal1(), input.getSeal2(), input.getSeal3(), input.getSeal4(), input.getUserRegistrationId(),
                input.getLanguageId(), input.getRemarks(), "sd1_rule_integration_sdy", "gateout", input.getOperationCode());

        if (!avoidYard) {
            yardIntegrationRule(responseAssignGout, inputRoot);
        }
        try {
            entityManager.flush();
            entityManager.clear();
        } catch (Exception e) {
            throw new PersistenceException(e);
        }
        return responseAssignGout;
    }

    public Object yardIntegrationRule(Map<String, Object> responseAssignGout, SdgTruckDepartureRegisterInput.Root inputRoot) throws IOException {
        SdgTruckDepartureRegisterInput.Input input = inputRoot.getSdg().getInput();

        if (responseAssignGout != null && responseAssignGout.get(INTEGRATION_DATA) != null) {
            JSONArray integrationDataArray = new JSONArray(responseAssignGout.get(INTEGRATION_DATA).toString());
            JSONObject integrationData = integrationDataArray.getJSONObject(0);
            if (integrationData.get("type_product_integration").equals("sdy")) {
                String sdyToken = getSdyToken(loginUrl, user, password, system);
                try {

                    JSONObject bodySdy = new JSONObject();
                    JSONObject bodyF = new JSONObject();
                    JSONObject body = new JSONObject();
                    JSONArray containersArray = new JSONArray().put(new JSONObject()
                            .put("numero_contenedor", integrationData.get("container_number"))
                            .put("eir_numero", input.getEirId()));

                    body.put("usuario_id", input.getUserRegistrationId())
                            .put("unidad_negocio_id", input.getSubBusinessUnitLocalId())
                            .put("tipo_operacion", input.getOperationCode())
                            .put("proceso_realizado", "GO")
                            .put("contenedores", containersArray);
                    bodyF.put("F", body);
                    bodySdy.put("SDY", bodyF);

                    URL url = new URL(gateOutUrl);
                    url.toURI();
                    HttpURLConnection httpConn = (HttpURLConnection) url.openConnection();
                    httpConn.setRequestMethod("POST");
                    httpConn.setRequestProperty("Authorization", "Bearer " + sdyToken);
                    httpConn.setRequestProperty("Content-Type", "application/json");
                    httpConn.setDoOutput(true);

                    try (OutputStream os = httpConn.getOutputStream()) {
                        byte[] inputStream = bodySdy.toString().getBytes(StandardCharsets.UTF_8);
                        os.write(inputStream, 0, inputStream.length);
                    }

                    try (BufferedReader br = new BufferedReader(new InputStreamReader(httpConn.getInputStream(), StandardCharsets.UTF_8))) {
                        StringBuilder response = new StringBuilder();
                        String responseLine;
                        while ((responseLine = br.readLine()) != null) {
                            response.append(responseLine.trim());
                        }
                        JSONObject jsonResponse = new JSONObject(response.toString());

                        if (jsonResponse.getBoolean(IS_CORRECT) &&
                                "Success".equals(jsonResponse.getString("status")) &&
                                jsonResponse.get(RESULT) != null) {
                            JSONObject jsonResult = jsonResponse.getJSONObject(RESULT);
                            if (jsonResult.getBoolean(IS_CORRECT)) {
                                integrationData.put("message", "SDY - Container successfully assigned to exit");
                                integrationDataArray.put(0, integrationData);
                                responseAssignGout.put(INTEGRATION_DATA, integrationDataArray.toString());
                                return responseAssignGout;
                            } else {
                                return BAD_RESPONSE;
                            }
                        } else {
                            return BAD_RESPONSE;
                        }
                    }
                } catch (Exception e) {
                    return BAD_RESPONSE;
                }
            }
        }
        return BAD_RESPONSE;
    }

    public String getSdyToken(String loginUrl, String username, String password, String system) throws IOException {

        JSONObject body = new JSONObject();
        body.put("username", username);
        body.put("password", password);
        body.put("system", system);

        URL url = new URL(loginUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        try {
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json; utf-8");
            connection.setRequestProperty("Accept", "application/json");
            connection.setDoOutput(true);

            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = body.toString().getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            int status = connection.getResponseCode();
            if (status >= HttpURLConnection.HTTP_OK && status < HttpURLConnection.HTTP_MULT_CHOICE) {
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    StringBuilder response = new StringBuilder();
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        response.append(responseLine.trim());
                    }
                    JSONObject jsonResponse = new JSONObject(response.toString());

                    if (jsonResponse.getBoolean(IS_CORRECT) && jsonResponse.has("token")) {
                        return jsonResponse.getString("token");
                    } else {
                        throw new IOException("Invalid response: " + jsonResponse.toString());
                    }
                }
            } else {
                throw new IOException("HTTP Request Failed with status code: " + status);
            }
        } finally {
            connection.disconnect();
        }
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        context = applicationContext;
    }
}
