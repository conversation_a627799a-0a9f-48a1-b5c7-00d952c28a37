package com.maersk.sd1.sdg.controller.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class BookingPreAllocationContainerListOutput {
    @JsonProperty("depot")
    private String depot;

    @JsonProperty("empty_full")
    private String emptyFull;

    @JsonProperty("equipment_number")
    private String equipmentNumber;

    @JsonProperty("equipment_category")
    private String equipmentCategory;

    @JsonProperty("equipment_size_type")
    private String equipmentSizeType;

    @JsonProperty("equipment_grade")
    private String equipmentGrade;

    @JsonProperty("iso_code")
    private String isoCode;

    @JsonProperty("shipping_line")
    private String shippingLine;

    @JsonProperty("shipper_name")
    private String shipperName;

    @JsonProperty("commodity")
    private String commodity;

    @JsonProperty("gate_in_date")
    private String gateInDate;

    @JsonProperty("dwell_time")
    private String dwellTime;

    @JsonProperty("structure_condition_current")
    private String structureConditionCurrent;

    @JsonProperty("machinery_condition_current")
    private String machineryConditionCurrent;

    @JsonProperty("equipment_restriction")
    private String equipmentRestriction;

    @JsonProperty("usda_approved")
    private String usdaApproved;

    @JsonProperty("gate_in")
    private String gateIn;

    @JsonProperty("structure_condition_inspection")
    private String structureConditionInspection;

    @JsonProperty("machinery_condition_inspection")
    private String machineryConditionInspection;

    @JsonProperty("gate_in_comment")
    private String gateInComment;

    @JsonProperty("inspector_comment")
    private String inspectorComment;

    @JsonProperty("booking_pre_allocation")
    private String bookingPreAllocation;

    @JsonProperty("potential_fgis")
    private String potentialFgis;

    @JsonProperty("equipment_size_id")
    private Integer equipmentSizeId;

    @JsonProperty("equipment_type_id")
    private Integer equipmentTypeId;

    @JsonProperty("container_id")
    private Integer containerId;

    @JsonProperty("total")
    private Integer total;


}
