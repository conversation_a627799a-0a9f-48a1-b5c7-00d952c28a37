package com.maersk.sd1.sdg.service;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.maersk.sd1.common.dto.AttachmentLoadFilesDTO;
import com.maersk.sd1.common.dto.LinesWithSubLinesDTO;
import com.maersk.sd1.common.dto.SystemRuleDTO;
import com.maersk.sd1.common.exception.SD1ControlledException;
import com.maersk.sd1.common.exception.SD1Exception;
import com.maersk.sd1.common.integration.*;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sdg.controller.dto.GateInGeneralRegisterV2Input;
import com.maersk.sd1.sdg.controller.dto.GateInGeneralRegisterV2Output;
import com.maersk.sd1.sdg.controller.dto.GateOutGeneralRegisterInput;
import com.maersk.sd1.sdg.controller.dto.GateOutGeneralRegisterOutput;
import com.maersk.sd1.sdg.dto.GateInGeneralRegisterV2Chassis;
import com.maersk.sd1.sdg.dto.GateInGeneralRegisterV2Equipment;
import com.maersk.sd1.sdg.dto.GateInGeneralRegisterV2Imo;
import com.maersk.sd1.sdg.repository.GateInGeneralRegisterV2Repository;
import lombok.RequiredArgsConstructor;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.System;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

import static com.maersk.sd1.common.Constants.*;
import static com.maersk.sd1.common.Parameter.*;

@Service
@RequiredArgsConstructor
public class GateInGeneralRegisterV2Service {

    private final GESCatalogService catalogService;
    private final BusinessUnitRepository businessUnitRepository;
    private final MessageLanguageService messageLanguageService;
    private final ContainerRepository containerRepository;
    private final IsoCodeRepository isoCodeRepository;
    private final GateInGeneralRegisterV2Repository gateInGeneralRegisterV2Repository;
    private final SystemRuleRepository systemRuleRepository;
    private final EirRepository eirRepository;
    private final EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;
    private final EirZoneRepository eirZoneRepository;
    private final EmrInspectionRepository emrInspectionRepository;
    private final VesselProgrammingContainerRepository vesselProgrammingContainerRepository;
    private final TransportPlanningDetailRepository transportPlanningDetailRepository;
    private final AttachmentRepository attachmentRepository;
    private final VesselProgrammingContainerImoRepository vesselProgrammingContainerImoRepository;
    private final SdfEirPhotoRepository sdfEirPhotoRepository;
    private final SdsEirPhotoRepository sdsEirPhotoRepository;
    private final EirMultipleRepository eirMultipleRepository;
    private final ChassisRepository chassisRepository;
    private final ShippingLineRepository shippingLineRepository;
    private final VesselRepository vesselRepository;
    private final VesselProgrammingRepository vesselProgrammingRepository;
    private final VesselProgrammingDetailRepository vesselProgrammingDetailRepository;
    private final EirChassisZoneRepository eirChassisZoneRepository;
    private final EirChassisRepository eirChassisRepository;
    private final ChassisDocumentDetailRepository chassisDocumentDetailRepository;
    private final CatalogRepository catalogRepository;
    private final EirChassisZoneActivityRepository eirChassisZoneActivityRepository;
    private final ChassisDocumentRepository chassisDocumentRepository;
    private final StockChassisRepository stockChassisRepository;
    private final CargoDocumentDetailRepository cargoDocumentDetailRepository;

    @Autowired
    private Environment env;
    @Autowired
    private SD1IntegrationConnectionFactory connectionFactory;
    @Lazy
    @Autowired
    private GateOutGeneralRegisterService gateOutGeneralRegisterService;

    @Transactional
    public GateInGeneralRegisterV2Output.Output registerGateInTransactional(GateInGeneralRegisterV2Input.Root request) throws Exception {
        return registerGateIn(request.getPrefix().getInput());
    }

    public GateInGeneralRegisterV2Output.Output registerGateInNonTransactional(GateInGeneralRegisterV2Input.Root request) throws Exception {
        return registerGateIn(request.getPrefix().getInput());
    }

    private GateInGeneralRegisterV2Output.Output registerGateIn(GateInGeneralRegisterV2Input.Input gateInInput) throws Exception {
        GateInGeneralRegisterV2Output.Output gateInOutput;
        gateInInput.setSystemRuleId("sd1_rule_integration_sdy");
        gateInInput.setTypeProcess("gatein");

        gateInOutput = execute(gateInInput);

        if (Integer.valueOf(1).equals(Optional.ofNullable(gateInOutput).map(GateInGeneralRegisterV2Output.Output::getResult).map(GateInGeneralRegisterV2Output.Result::getResultState).orElse(null))
                && !gateInInput.getFlagChassisStayed() && !gateInInput.getChassis().equals("{}")) {
            GateOutGeneralRegisterOutput gateOutOutput = implementGateOut(gateInInput);
            if (!gateOutOutput.getResultState().equals(1)) {
                //ROLLBACK
                throw new SD1Exception(this.getClass().getName(), gateOutOutput.getResultMessage());
            }
        }

        return gateInOutput;
    }

    private GateOutGeneralRegisterOutput implementGateOut(GateInGeneralRegisterV2Input.Input gateInInput) throws Exception {
        System.out.println("In gateout");
        HashMap<String, HashMap<String, Object>> spResults = new HashMap<>();
        GateOutGeneralRegisterOutput gateOutOutput;
        Integer gateInChassisId;
        Integer documentChassisGateOutId;
        HashMap<String, Object> resultChassisGateOut = chassisDocumentRepository.documentChassisGateOutGenerated(gateInInput.getSubBusinessUnitLocalId(), gateInInput.getChassis(), gateInInput.getUserRegistrationId(), gateInInput.getLanguageId());

        if (resultChassisGateOut != null && resultChassisGateOut.get(RESULT_STATE) != null && resultChassisGateOut.get(RESULT_STATE).equals(1)) {
            System.out.println("gateoutttt2");
            spResults.put("resultChassisGateOut", resultChassisGateOut);
            documentChassisGateOutId = Integer.valueOf(resultChassisGateOut.get("result_doc_gateout_id").toString());
            gateInChassisId = Integer.valueOf(resultChassisGateOut.get("result_chassis_id").toString());

            GateOutGeneralRegisterInput.Root gateOutRequest = GateOutGeneralRegisterInput.Root.builder()
                    .prefix(GateOutGeneralRegisterInput.Prefix.builder()
                            .input(GateOutGeneralRegisterInput.Input.generateDummyGateOutInput(gateInInput, documentChassisGateOutId))
                            .build())
                    .build();

            gateOutOutput = gateOutGeneralRegisterService.gateOutGeneralRegisterNonTransactional(gateOutRequest);
            if (gateOutOutput != null && gateOutOutput.getResultState().equals(1)) {
                HashMap<String, Object> gateOutAssignmentResult = eirRepository.gateoutGeneralAssignmentRegisterV2(
                        gateInInput.getSubBusinessUnitLocalId(),
                        gateOutOutput.getResultNewId(),
                        null,
                        gateInChassisId,
                        null,
                        null,
                        "[]",
                        null,
                        null,
                        null,
                        null,
                        gateInInput.getUserRegistrationId(),
                        gateInInput.getLanguageId(),
                        gateInInput.getComments(),
                        null,
                        gateInInput.getTypeProcess(),
                        "POSNMTO"
                );

                if (gateOutAssignmentResult == null || gateOutAssignmentResult.get(RESULT_STATE) == null && !gateOutAssignmentResult.get(RESULT_STATE).equals(1)) {
                    gateOutOutput = GateOutGeneralRegisterOutput.builder()
                            .resultNewId(0)
                            .resultState(2)
                            .resultMessage("GateoutGeneralAssignmentRegister -> " + Optional.ofNullable(gateOutAssignmentResult).map(hm -> hm.get("result_message")).map(Object::toString).orElse(""))
                            .build();
                }
            } else {
                gateOutOutput = GateOutGeneralRegisterOutput.builder()
                        .resultNewId(0)
                        .resultState(2)
                        .resultMessage("GateoutGeneralRegister -> " + Optional.ofNullable(gateOutOutput).map(GateOutGeneralRegisterOutput::getResultMessage).orElse(""))
                        .build();
            }
        } else {
            gateOutOutput = GateOutGeneralRegisterOutput.builder()
                    .resultNewId(0)
                    .resultState(2)
                    .resultMessage("DocForGateinOptimized -> " + Optional.ofNullable(resultChassisGateOut).map(hm -> hm.get("result_message")).map(Object::toString).orElse(null))
                    .build();
        }
        return gateOutOutput;
    }

    private GateInGeneralRegisterV2Output.Output execute(GateInGeneralRegisterV2Input.Input input) {
        //VARIABLES
        String validateChassisStockMessage = "";
        String typeProductIntegration = null;
        Integer resultState = 0;
        Integer programacionNaveDetalleId;
        Integer documentoCargaDetalleId;
        Integer planningDetailId;
        Integer equipmentId;
        Integer isoCodeId;
        Integer manifestWeightUnitId;
        Integer temperatureUnitId;
        Integer eirId;
        Integer eirChassisId = null;
        Integer apsId = null;
        Integer catEquipMeasureTareId;
        Integer catEquipMeasurePayloadId;
        Integer catMarcaMotorId;
        Integer gradeId;
        Integer reeferTypeId;
        Integer shippinglineBl;
        Integer equipmentTypeId;
        Integer equipmentSizeId;
        Integer equipmentFamilyId;
        Integer isoCodeCurrentId;
        Integer consigneeId;
        Integer chassisId;
        Integer chassisTypeId;
        Integer documentChassisDetailId;
        Integer programacionNaveId;
        Integer vesselId;
        Integer depotOperationId;
        Integer tare;
        Integer payLoad;
        Integer catCargoDocumentTypeId;
        Integer subBusinessUnitId = businessUnitRepository.findParentBusinessUnitId(input.getSubBusinessUnitLocalId());
        Integer businessUnitId = businessUnitRepository.findParentBusinessUnitId(subBusinessUnitId);
        String equipmentNumber;
        String temperature;
        String seal1;
        String seal2;
        String seal3;
        String seal4;
        String adjuntos;
        String documentoCargaReferencia;
        String chassisNumber;
        String voyage = "0";
        String vessel = "VARIOUS";
        String isReefer;
        String isReeferManif;
        BigDecimal manifestWeight;
        Boolean reeferConnect;
        Boolean reeferDangerous;
        Boolean isOnlyChassis = false;
        Boolean noMaersk = null;
        Boolean boxWithDamage = false;
        Boolean boxDamaged = false;
        Boolean machineryWithDamage = false;
        Boolean machineryDamaged = false;
        Short reviewControl = null;
        Short allocationControl = null;
        Short allocationLightControl = null;
        LocalDateTime truckInDate = LocalDateTime.now();
        LocalDateTime reviewDate = null;
        LocalDateTime goutInspeccionDate = null;
        LocalDateTime manufacture;
        Integer catInsertSourceCntId;
        Integer catUpdateSourceCntId;

        //CATALOGS
        List<String> catalogAliases = Arrays.asList(
                CATALOG_TYPE_GATE_IS_GATEIN_ALIAS,
                CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS,
                CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS,
                CATALOG_TYPE_PROCESS_IS_FULL_ALIAS,
                CATALOG_MEASURE_WEIGHT_KG_ALIAS,
                CATALOG_MEANS_TRANSPORT,
                CATALOG_NO_GRADE,
                CATALOG_PENDING_INSPECTION,
                CATALOG_DEPOT_OPER_MTY_DISCHARGE,
                CATALOG_GI_TYPE_MTY_DISCHARGE,
                CATALOG_GI_GENERAL_CREATION,
                CATALOG_CUSTOMER_RETURN,
                CATALOG_CHASSIS_IN_PROGRESS,
                CATALOG_TRK_PLAN_IN_PROCESS,
                CATALOG_CONTAINER_CREATION_SOURCE_LOAD_MASTER,
                CATALOG_CONTAINER_CREATION_SOURCE_FLEET_EDI,
                CATALOG_CONTAINER_UPDATE_SOURCE_FLEET_EDI
        );
        HashMap<String, Integer> catalogIds = catalogService.findIdsByAliases(catalogAliases);

        System.out.println("catalogIds: " + catalogIds);

        //UTIL
        Gson gson = new Gson();
        List<GateInGeneralRegisterV2Equipment> equipmentInput;
        GateInGeneralRegisterV2Chassis chassisInput;
        Pageable pageable = PageRequest.of(0, 1);

        if (input.getInputChassisNumber() != null && !input.getInputChassisNumber().isBlank()) {
            input.setFlagChassisStayed("0");
        }

        //VALIDATION
        if ((subBusinessUnitId == null || businessUnitId == null || input.getSubBusinessUnitLocalId() == null || input.getDriverId() == null || input.getVehicleId() == null || input.getTruckCompanyId() == null)
                || (input.getEquipmentCatEmptyFullId() == null && (input.getEquipment() == null || input.getEquipment().isBlank()) && (input.getChassis() == null || input.getChassis().isBlank()))
                || ((input.getEquipmentCatEmptyFullId() == null && !input.getEquipment().equals("[]")) || (input.getEquipmentCatEmptyFullId() != null && (input.getEquipment() == null || input.getEquipment().isBlank())))
                || (input.getEquipmentCatEmptyFullId()!=null && input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)) && input.getMoveTypeId() == null)
                || (input.getEquipmentCatEmptyFullId() != null && !input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)) && !input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS)))
        ) {
            return GateInGeneralRegisterV2Output.Output.builder()
                    .result(GateInGeneralRegisterV2Output.Result.builder()
                            .resultState(3)
                            .resultMessage(messageLanguageService.getMessage(GENERAL, 21, input.getLanguageId()))
                            .build())
                    .build();
        }

        Type equipmentListType = new TypeToken<ArrayList<GateInGeneralRegisterV2Equipment>>() {
        }.getType();
        Type chassisListType = new TypeToken<GateInGeneralRegisterV2Chassis>() {
        }.getType();

        equipmentInput = gson.fromJson(input.getEquipment(), equipmentListType);
        chassisInput = gson.fromJson(input.getChassis(), chassisListType);

        if (equipmentInput.isEmpty() && chassisInput.getChassisId() == null) {
            return GateInGeneralRegisterV2Output.Output.builder()
                    .result(GateInGeneralRegisterV2Output.Result.builder()
                            .resultState(3)
                            .resultMessage(messageLanguageService.getMessage(GENERAL, 21, input.getLanguageId()))
                            .build())
                    .build();
        }

        if (!equipmentInput.isEmpty()) {
            for (GateInGeneralRegisterV2Equipment ei : equipmentInput) {

                String equipmentMessage;
                String demurrateDate = null;
                Integer equipmentSizeManifId;
                Integer equipmentTypeManifId;

                equipmentId = ei.getEquipmentId();
                programacionNaveDetalleId = ei.getProgramacionNaveDetalleId();
                documentoCargaDetalleId = ei.getDocumentoCargaDetalleId();
                planningDetailId = ei.getPlanningDetailId();
                isoCodeId = ei.getIsoCodeId();
                manifestWeight = ei.getManifestWeight() != null ? ei.getManifestWeight() : BigDecimal.ZERO;
                manifestWeightUnitId = ei.getManifestWeightUnitId();
                temperature = ei.getTemperature();
                temperatureUnitId = ei.getTemperatureUnitId();
                seal1 = ei.getSeal1().trim().toUpperCase();
                seal2 = ei.getSeal2().trim().toUpperCase();
                seal3 = ei.getSeal3().trim().toUpperCase();
                seal4 = ei.getSeal4().trim().toUpperCase();
                reeferConnect = Optional.ofNullable(ei.getReeferConnect()).orElse(false);
                reeferDangerous = Optional.ofNullable(ei.getReeferDangerous()).orElse(false);
                adjuntos = ei.getAdjuntos().toString();
                apsId = ei.getApsId();
                noMaersk = ei.getNoMaersk();

                if (ei.getEquipmentId() == null || ei.getProgramacionNaveDetalleId() == null || ei.getDocumentoCargaDetalleId() == null) {
                    resultState = 3;
                    ei.setEquipmentMessage(messageLanguageService.getMessage(GENERAL, 21, input.getLanguageId()));
                    continue;
                }

                Container equipmentContainer = containerRepository.findById(equipmentId).get();
                tare = equipmentContainer.getContainerTare();
                catEquipMeasureTareId = Optional.ofNullable(equipmentContainer.getCatEquipMeasureTare()).map(Catalog::getId).orElse(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS));
                payLoad = equipmentContainer.getMaximunPayload();
                catEquipMeasurePayloadId = Optional.ofNullable(equipmentContainer.getCatEquipMeasurePayload()).map(Catalog::getId).orElse(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS));
                gradeId = Optional.ofNullable(equipmentContainer.getCatGrade()).map(Catalog::getId).orElse(null);
                manufacture = equipmentContainer.getManufactureDate();
                catMarcaMotorId = Optional.ofNullable(equipmentContainer.getCatEngineBrand()).map(Catalog::getId).orElse(null);
                reeferTypeId = Optional.ofNullable(equipmentContainer.getCatReeferType()).map(Catalog::getId).orElse(null);
                equipmentNumber = equipmentContainer.getContainerNumber();
                isoCodeCurrentId = Optional.ofNullable(equipmentContainer.getIsoCode()).map(IsoCode::getId).orElse(null);
                equipmentSizeId = Optional.ofNullable(equipmentContainer.getCatSize()).map(Catalog::getId).orElse(null);
                equipmentTypeId = Optional.ofNullable(equipmentContainer.getCatContainerType()).map(Catalog::getId).orElse(null);
                catInsertSourceCntId = Optional.ofNullable(equipmentContainer).map(Container::getCatCreationOrigin).map(Catalog::getId).orElse(null);
                catUpdateSourceCntId = Optional.ofNullable(equipmentContainer).map(Container::getCatUpdatesourceCnt).map(Catalog::getId).orElse(null);

                if (noMaersk != null && Boolean.TRUE.equals(noMaersk)) {
                    // (04/MAY/2025) If has not association with "fleet file", noMaersk is false -- revalidation-reinforce.
                    List<Integer> fleetFileCatalogsList = List.of(
                            catalogIds.get(CATALOG_CONTAINER_CREATION_SOURCE_LOAD_MASTER),
                            catalogIds.get(CATALOG_CONTAINER_CREATION_SOURCE_FLEET_EDI),
                            catalogIds.get(CATALOG_CONTAINER_UPDATE_SOURCE_FLEET_EDI)
                    );

                    if (fleetFileCatalogsList.contains(Optional.ofNullable(catInsertSourceCntId).orElse(0)) ||
                            fleetFileCatalogsList.contains(Optional.ofNullable(catUpdateSourceCntId).orElse(0))) {
                        noMaersk = false;
                    }
                }

                if (input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS))) {
                    Optional<IsoCode> isoCode = isoCodeRepository.findById(isoCodeId);
                    equipmentSizeId = isoCode.map(IsoCode::getCatSize).map(Catalog::getId).orElse(null);
                    equipmentTypeId = isoCode.map(IsoCode::getCatContainerType).map(Catalog::getId).orElse(null);
                    isReefer = isoCode.map(IsoCode::getCatContainerType).map(Catalog::getCode).orElse(null);

                    if (isReefer == null) {
                        reeferTypeId = null;
                        catMarcaMotorId = null;
                    } else {
                        reeferTypeId = containerRepository.fnGetDefaultReeferType(equipmentNumber);
                    }

                    if (tare.equals(0) || payLoad.equals(0)) {
                        HashMap<String, Object> defaultIsoCode = isoCodeRepository.getDefaultIsoCodeContainer(equipmentSizeId, equipmentTypeId);
                        isoCodeId = Integer.parseInt(defaultIsoCode.get("iso_code_id").toString());
                        tare = Integer.parseInt(defaultIsoCode.get("tare").toString());
                        payLoad = Integer.parseInt(defaultIsoCode.get("payload").toString());
                    }

                    manifestWeight = manifestWeight.equals(BigDecimal.ZERO) ? BigDecimal.ONE : manifestWeight;
                    manifestWeightUnitId = manifestWeightUnitId == null ? catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS) : manifestWeightUnitId;
                }

                if (gradeId == null) {
                    gradeId = catalogIds.get(CATALOG_NO_GRADE);
                }

                Optional<CargoDocumentDetail> cargoDocumentDetail = gateInGeneralRegisterV2Repository.getActiveCargoDocumentByDetailId(documentoCargaDetalleId);
                Optional<VesselProgrammingContainer> vesselProgrammingContainer = gateInGeneralRegisterV2Repository
                        .getVesselProgrammingContainer(
                                pageable,
                                cargoDocumentDetail.map(CargoDocumentDetail::getCargoDocument).map(CargoDocument::getVesselProgrammingDetail).map(VesselProgrammingDetail::getId).orElse(0),
                                cargoDocumentDetail.map(CargoDocumentDetail::getContainer).map(Container::getId).orElse(0)).stream().findFirst();

                consigneeId = cargoDocumentDetail.map(CargoDocumentDetail::getCargoDocument).map(CargoDocument::getConsigneeCompany).map(Company::getId).orElse(null);
                shippinglineBl = cargoDocumentDetail.map(CargoDocumentDetail::getCargoDocument).map(CargoDocument::getShippingLine).map(ShippingLine::getId).orElse(null);
                equipmentSizeManifId = vesselProgrammingContainer.map(VesselProgrammingContainer::getCatManifestedSize).map(Catalog::getId).orElse(null);
                equipmentTypeManifId = vesselProgrammingContainer.map(VesselProgrammingContainer::getCatManifestedContainerType).map(Catalog::getId).orElse(null);
                isReeferManif = vesselProgrammingContainer.map(VesselProgrammingContainer::getCatManifestedContainerType).map(Catalog::getCode).orElse(null);
                documentoCargaReferencia = cargoDocumentDetail.map(CargoDocumentDetail::getCargoDocument).map(CargoDocument::getCargoDocument).orElse(null);
                catCargoDocumentTypeId = cargoDocumentDetail.map(CargoDocumentDetail::getCargoDocument).map(CargoDocument::getCatCargoDocumentType).map(Catalog::getId).orElse(null);

                if (shippinglineBl != null) {
                    final Integer shippinglineBlTemp = shippinglineBl;
                    SystemRule systemRule = systemRuleRepository.findByAliasAndActiveTrue("sds_subshippingline_equivalence");
                    Type subLinesListType = new TypeToken<ArrayList<LinesWithSubLinesDTO>>() {
                    }.getType();
                    List<LinesWithSubLinesDTO> linesWithSubLinesList = gson.fromJson(systemRule.getRule(), subLinesListType);
                    if (linesWithSubLinesList.stream().anyMatch(sb -> sb.getSubLineId().equals(shippinglineBlTemp))) {
                        shippinglineBl = linesWithSubLinesList.stream().filter(sb -> sb.getSubLineId().equals(shippinglineBlTemp)).findFirst().get().getLineMainId(); //ORELSE
                    }
                }

                if (input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS))) {
                    manifestWeight = BigDecimal.ZERO;
                    manifestWeightUnitId = null;

                    if (tare.equals(0) || isoCodeCurrentId == null || payLoad.equals(0)) {
                        HashMap<String, Object> defaultIsoCode = isoCodeRepository.getDefaultIsoCodeContainer(equipmentSizeManifId, equipmentTypeManifId);
                        isoCodeCurrentId = Integer.parseInt(defaultIsoCode.get("iso_code_id").toString());
                        tare = Integer.parseInt(defaultIsoCode.get("tare").toString());
                        payLoad = Integer.parseInt(defaultIsoCode.get("payload").toString());
                    }

                    if (equipmentTypeManifId != null && !Objects.equals(equipmentTypeId, equipmentTypeManifId)) {
                        if (isReeferManif == null) {
                            reeferTypeId = null;
                            catMarcaMotorId = null;
                        } else if (reeferTypeId == null) {
                            reeferTypeId = containerRepository.fnGetDefaultReeferType(equipmentNumber);
                        }
                    }
                    equipmentSizeId = equipmentSizeManifId;
                    equipmentTypeId = equipmentTypeManifId;
                    isoCodeId = isoCodeCurrentId;
                    reviewControl = 0;

                    if (input.getMoveTypeId().equals(catalogIds.get(CATALOG_CUSTOMER_RETURN))) {
                        VesselProgrammingContainer vpc = gateInGeneralRegisterV2Repository.getVesselProgrammingContainer(pageable, programacionNaveDetalleId, equipmentId).stream().findFirst().orElse(null);
                        demurrateDate = Optional.ofNullable(vpc).map(VesselProgrammingContainer::getDemurrageDate).map(dd -> dd.format((DateTimeFormatter.ofPattern("yyyyMMdd")))).orElse(null);
                    }

                    Optional<SystemRule> apsMandatorySystemRule = systemRuleRepository.findByAliasAndBusinessUnitId("sde_tipo_mov_cita_obligario", businessUnitId, subBusinessUnitId);
                    if (apsMandatorySystemRule.isPresent()) {
                        Type integerListType = new TypeToken<ArrayList<Integer>>() {
                        }.getType();
                        List<Integer> moveTypeIds = gson.fromJson(apsMandatorySystemRule.get().getRule(), integerListType);
                        if (apsId != null && moveTypeIds.stream().anyMatch(mt -> mt.equals(input.getMoveTypeId()))) {
                            ei.setEquipmentMessage(messageLanguageService.getMessage("PRC_GATE_IN_EMPTY", 9, input.getLanguageId()));
                            continue;
                        }
                    }
                }

                equipmentMessage = cargoDocumentDetailRepository.validateContainerStock(businessUnitId, subBusinessUnitId, catalogIds.get(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS), input.getEquipmentCatEmptyFullId(), equipmentId, equipmentNumber, 1, catalogIds.get(CATALOG_GI_GENERAL_CREATION), "adjust stock gate in", input.getLanguageId());
                if (!equipmentMessage.isEmpty()) {
                    ei.setEquipmentMessage(equipmentMessage);
                    continue;
                }

                Eir newEquipmentEir = Eir.builder()
                        .businessUnit(BusinessUnit.builder().id(businessUnitId).build())
                        .subBusinessUnit(BusinessUnit.builder().id(subBusinessUnitId).build())
                        .localSubBusinessUnit(BusinessUnit.builder().id(input.getSubBusinessUnitLocalId()).build())
                        .catMovement(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS)).build())
                        .catEmptyFull(Catalog.builder().id(input.getEquipmentCatEmptyFullId()).build())
                        .catOrigin(Optional.ofNullable(input.getMoveTypeId()).map(mt -> Catalog.builder().id(mt).build()).orElse(null))
                        .truckArrivalDate(truckInDate)
                        .vesselProgrammingDetail(Optional.ofNullable(programacionNaveDetalleId).map(pnd -> VesselProgrammingDetail.builder().id(pnd).build()).orElse(null))
                        .container(Container.builder().id(equipmentId).build())
                        .transportCompany(Company.builder().id(input.getTruckCompanyId()).build())
                        .truck(Truck.builder().id(input.getVehicleId()).build())
                        .driverPerson(Person.builder().id(input.getDriverId()).build())
                        .shippingLine(Optional.ofNullable(shippinglineBl).map(sl -> ShippingLine.builder().id(sl).build()).orElse(null))
                        .seal1(seal1)
                        .seal2(seal2)
                        .seal3(seal3)
                        .seal4(seal4)
                        .taraCnt(tare)
                        .cargoMaximumCnt(payLoad)
                        .isoCode(Optional.ofNullable(isoCodeId).map(ic -> IsoCode.builder().id(ic).build()).orElse(null))
                        .catContainerType(Optional.ofNullable(equipmentTypeId).map(et -> Catalog.builder().id(et).build()).orElse(null))
                        .catSizeCnt(Optional.ofNullable(equipmentSizeId).map(es -> Catalog.builder().id(es).build()).orElse(null))
                        .catClassCnt(Catalog.builder().id(gradeId).build())
                        .catTypeReefer(Optional.ofNullable(reeferTypeId).map(rt -> Catalog.builder().id(rt).build()).orElse(null))
                        .catEngineBrand(Optional.ofNullable(catMarcaMotorId).map(mm -> Catalog.builder().id(mm).build()).orElse(null))
                        .dateManufacture(manufacture)
                        .observation(input.getComments())
                        .structureWithDamage(boxWithDamage)
                        .structureDamaged(boxDamaged)
                        .machineryWithDamage(machineryWithDamage)
                        .machineryDamaged(machineryDamaged)
                        .controlRevision(reviewControl)
                        .dateRevision(reviewDate)
                        .controlAssignment(allocationControl)
                        .dateGateOutInspection(goutInspeccionDate)
                        .truckMultipleLoad(false)
                        .truckRetreatWithLoad(false)
                        .active(true)
                        .catCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_GI_GENERAL_CREATION)).build())
                        .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                        .registrationDate(LocalDateTime.now())
                        .clientCompany(Optional.ofNullable(consigneeId).map(cs -> Company.builder().id(cs).build()).orElse(null))
                        .traceEir("ins_gi_general")
                        .controlAssignmentLight(allocationLightControl)
                        .weightGoods(manifestWeight)
                        .catMeasureWeight(Optional.ofNullable(manifestWeightUnitId).map(mwi -> Catalog.builder().id(mwi).build()).orElse(null))
                        .catMeansTransport(Catalog.builder().id(catalogIds.get(CATALOG_MEANS_TRANSPORT)).build())
                        .catMeasureTare(Optional.ofNullable(catEquipMeasureTareId).map(mt -> Catalog.builder().id(mt).build()).orElse(null))
                        .catMeasurePayload(Optional.ofNullable(catEquipMeasurePayloadId).map(mp -> Catalog.builder().id(mp).build()).orElse(null))
                        .transportPlanningDetailFull(Optional.ofNullable(planningDetailId).map(pd -> TransportPlanningDetail.builder().id(pd).build()).orElse(null))
                        .returnDateH(Optional.ofNullable(demurrateDate).map(dd -> LocalDate.parse(dd, DateTimeFormatter.ofPattern("yyyyMMdd")).atStartOfDay()).orElse(null))
                        .externalDocumentNumber(apsId.toString())
                        .chassisNumber(input.getInputChassisNumber())
                        .flagChassisStayed(input.getFlagChassisStayed())
                        .flagOnSiteInspection(input.getInspectionSitu())
                        .flagNoMaersk(noMaersk)
                        .numberTwr(input.getTwrNumber())
                        .build();

                newEquipmentEir = eirRepository.save(newEquipmentEir);
                eirId = Optional.ofNullable(newEquipmentEir).map(Eir::getId).orElse(null);

                EirDocumentCargoDetail eirDocumentCargoDetail = EirDocumentCargoDetail.builder()
                        .eir(newEquipmentEir)
                        .cargoDocumentDetail(Optional.ofNullable(documentoCargaDetalleId).map(dcd -> CargoDocumentDetail.builder().id(dcd).build()).orElse(null))
                        .active(true)
                        .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                        .registrationDate(LocalDateTime.now())
                        .documentCargoReference(documentoCargaReferencia)
                        .catCargoDocumentType(Optional.ofNullable(catCargoDocumentTypeId).map(cdt -> Catalog.builder().id(cdt).build()).orElse(null))
                        .build();

                eirDocumentCargoDetailRepository.save(eirDocumentCargoDetail);

                equipmentContainer.setIsoCode(Optional.ofNullable(isoCodeId).map(ic -> IsoCode.builder().id(ic).build()).orElse(null));
                equipmentContainer.setCatContainerType(Optional.ofNullable(equipmentTypeId).map(et -> Catalog.builder().id(et).build()).orElse(null));
                equipmentContainer.setCatSize(Optional.ofNullable(equipmentSizeId).map(es -> Catalog.builder().id(es).build()).orElse(null));
                equipmentContainer.setCatEquipMeasureTare(Optional.ofNullable(catEquipMeasureTareId).map(emt -> Catalog.builder().id(emt).build()).orElse(null));
                equipmentContainer.setCatEquipMeasurePayload(Optional.ofNullable(catEquipMeasurePayloadId).map(emp -> Catalog.builder().id(emp).build()).orElse(null));
                equipmentContainer.setShippingLine(Optional.ofNullable(shippinglineBl).map(sl -> ShippingLine.builder().id(sl).build()).orElse(null));
                equipmentContainer.setModificationUser(User.builder().id(input.getUserRegistrationId()).build());
                equipmentContainer.setModificationDate(LocalDateTime.now());
                equipmentContainer.setActive(true);
                equipmentContainer.setCatGrade(Optional.ofNullable(gradeId).map(gr -> Catalog.builder().id(gr).build()).orElse(null));
                containerRepository.save(equipmentContainer);

                if (input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS))) {
                    eirZoneRepository.firstActivityZoneEmptyContainer(eirId, catalogIds.get(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS), input.getEquipmentCatEmptyFullId(), input.getUserRegistrationId(), catalogIds.get(CATALOG_GI_GENERAL_CREATION), equipmentTypeId);
                }

                VesselProgrammingContainer equipmentVesselProgrammingContainer = gateInGeneralRegisterV2Repository.getVesselProgrammingContainer(pageable, programacionNaveDetalleId, equipmentId).stream().findFirst().orElse(null);
                if (input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS))) {
                    EmrInspection emrInspection = EmrInspection.builder()
                            .businessUnit(BusinessUnit.builder().id(businessUnitId).build())
                            .subBusinessUnit(BusinessUnit.builder().id(subBusinessUnitId).build())
                            .eir(newEquipmentEir)
                            .catStatus(Catalog.builder().id(catalogIds.get(CATALOG_PENDING_INSPECTION)).build())
                            .active(true)
                            .registrationDate(LocalDateTime.now())
                            .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                            .damageCount(0)
                            .flagDamageBox(false)
                            .flagDamageMachine(false)
                            .build();
                    emrInspectionRepository.save(emrInspection);

                    if (equipmentVesselProgrammingContainer != null) {
                        equipmentVesselProgrammingContainer.setReceivedTemperatureC(temperature);
                        equipmentVesselProgrammingContainer.setCatReceivedTemperatureMeasure(Optional.ofNullable(temperatureUnitId).map(tu -> Catalog.builder().id(tu).build()).orElse(null));
                        equipmentVesselProgrammingContainer.setIsDangerousCargo(reeferDangerous);
                        equipmentVesselProgrammingContainer.setIsRefrigeratedCargo(reeferConnect);
                        equipmentVesselProgrammingContainer.setModificationUser(User.builder().id(input.getUserRegistrationId()).build());
                        equipmentVesselProgrammingContainer.setModificationDate(LocalDateTime.now());
                        equipmentVesselProgrammingContainer.setTraceProgVesCnt("gi_general");
                        vesselProgrammingContainerRepository.save(equipmentVesselProgrammingContainer);
                    }

                    Optional<TransportPlanningDetail> transportPlanningDetailFull = transportPlanningDetailRepository.findById(planningDetailId);
                    if (transportPlanningDetailFull.isPresent()) {
                        transportPlanningDetailFull.get().setCatTrkPlanningState(Catalog.builder().id(catalogIds.get(CATALOG_TRK_PLAN_IN_PROCESS)).build());
                        transportPlanningDetailFull.get().setModificationUser(User.builder().id(input.getUserRegistrationId()).build());
                        transportPlanningDetailFull.get().setModificationDate(LocalDateTime.now());
                        transportPlanningDetailRepository.save(transportPlanningDetailFull.get());
                    }

                }

                if (adjuntos != null && !adjuntos.isBlank() && adjuntos.length() > 2) {
                    Type attachedResponseType = new TypeToken<ArrayList<AttachmentLoadFilesDTO>>() {
                    }.getType();
                    Map<String, Object> loadFileResponse = attachmentRepository.loadFiles(input.getUserRegistrationId(), adjuntos);
                    if (loadFileResponse.get("estado").equals(1)) {
                        List<AttachmentLoadFilesDTO> filesToLoad = gson.fromJson(loadFileResponse.get("respuesta").toString(), attachedResponseType);
                        filesToLoad = filesToLoad.stream().filter(lf -> lf.getAccion().equals('N')).toList();
                        if (input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS))) {
                            Eir finalNewEquipmentEir = newEquipmentEir;
                            filesToLoad.forEach(lf -> {
                                SdfEirPhoto eirPhoto = SdfEirPhoto.builder()
                                        .eir(finalNewEquipmentEir)
                                        .attached(Attachment.builder().id(lf.getAdjuntoId()).build())
                                        .active(true)
                                        .registrationDate(LocalDateTime.now())
                                        .userRegistration(User.builder().id(input.getUserRegistrationId()).build())
                                        .build();
                                sdfEirPhotoRepository.save(eirPhoto);
                            });
                        }
                        if (input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS))) {
                            Eir finalNewEquipmentEir1 = newEquipmentEir;
                            filesToLoad.forEach(lf -> {
                                SdsEirPhoto eirPhoto = SdsEirPhoto.builder()
                                        .eir(finalNewEquipmentEir1)
                                        .attachment(Attachment.builder().id(lf.getAdjuntoId()).build())
                                        .active(true)
                                        .registrationDate(LocalDateTime.now())
                                        .userRegistration(User.builder().id(input.getUserRegistrationId()).build())
                                        .build();
                                sdsEirPhotoRepository.save(eirPhoto);
                            });
                        }
                    }
                }

                ei.setEirId(eirId);
                ei.setEquipmentMessage(messageLanguageService.getMessage("PRC_GI_GENERAL", 1, input.getLanguageId()));

                if (!ei.getImos().isEmpty() && equipmentVesselProgrammingContainer != null) {
                    List<Integer> imoIdsList = ei.getImos().stream().map(im -> im.getImoId()).toList();
                    Integer deleted = vesselProgrammingContainerImoRepository.deleteByIdAndImoList(equipmentVesselProgrammingContainer.getId(), imoIdsList);
                    List<VesselProgrammingContainerImo> vesselProgrammingContainerImos = gateInGeneralRegisterV2Repository.getVesselProgrammingContainerImoByVesselProgrammingContainerId(equipmentVesselProgrammingContainer.getId());
                    List<Integer> vesselProgrammingContainerImosIds = vesselProgrammingContainerImos.stream().map(it -> it.getImo().getId()).toList();
                    List<GateInGeneralRegisterV2Imo> unregisteredImos = ei.getImos().stream().filter(im -> vesselProgrammingContainerImosIds.stream().noneMatch(id -> id.equals(im.getImoId()))).toList();
                    for (GateInGeneralRegisterV2Imo im : unregisteredImos) {
                        System.out.println("im.getImoId() = " + im.getImoId());
                        ProgrammingShipContainerImoId programmingShipContainerImoId = ProgrammingShipContainerImoId.builder()
                                .programmingShipContainerId(equipmentVesselProgrammingContainer.getId())
                                .imoId(im.getImoId())
                                .build();

                        VesselProgrammingContainerImo vesselProgrammingContainerImo = VesselProgrammingContainerImo.builder()
                                .id(programmingShipContainerImoId)
                                .vesselProgrammingContainer(VesselProgrammingContainer.builder()
                                        .id(equipmentVesselProgrammingContainer.getId())
                                        .build())
                                .imo(Imo.builder()
                                        .id(im.getImoId())
                                        .build())
                                .active(true)
                                .registrationUser(User.builder()
                                        .id(input.getUserRegistrationId())
                                        .build())
                                .registrationDate(LocalDateTime.now())
                                .build();

                        vesselProgrammingContainerImoRepository.save(vesselProgrammingContainerImo);
                    }
                }
            }

            //MULTIPLE RECEPTION
            if (equipmentInput.stream().filter(it -> it.getEirId() != null).toList().size() >= 2) {
                List<GateInGeneralRegisterV2Equipment> eirEquipments = equipmentInput.stream().filter(it -> it.getEirId() != null).toList();
                Integer eirGroupId = eirEquipments.stream().findFirst().map(GateInGeneralRegisterV2Equipment::getEirId).orElse(null);
                for (GateInGeneralRegisterV2Equipment equipment : eirEquipments) {
                    EirMultiple eirMultiple = EirMultiple.builder()
                            .id(EirMultipleId.builder().eirMultipleId(eirGroupId).build())
                            .eir(Eir.builder().id(equipment.getEirId()).build())
                            .active(true)
                            .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                            .registrationDate(LocalDateTime.now())
                            .build();

                    eirMultipleRepository.save(eirMultiple);

                    Eir eir = eirRepository.findById(equipment.getEirId()).orElse(null);
                    if (eir != null) {
                        eir.setTruckMultipleLoad(true);
                        eir.setTraceEir("gi_gral_multiple");
                        eir.setModificationUser(User.builder().id(input.getUserRegistrationId()).build());
                        eir.setModificationDate(LocalDateTime.now());
                        eirRepository.save(eir);
                    }
                }
            }
        }

        //GATE IN FOR CHASSIS
        if (chassisInput.getChassisId() != null) {
            if (!equipmentInput.isEmpty() && equipmentInput.stream().filter(eq -> eq.getEirId() != null).toList().isEmpty()) {
                return GateInGeneralRegisterV2Output.Output.builder()
                        .result(GateInGeneralRegisterV2Output.Result.builder()
                                .resultState(3)
                                .resultMessage(messageLanguageService.getMessage("PRC_CHASSIS_GI_GO", 3, input.getLanguageId()))
                                .build())
                        .build();
            }

            Optional<Chassis> topChassis = chassisRepository.findById(chassisInput.getChassisId());
            chassisId = topChassis.map(Chassis::getId).orElse(null);
            chassisNumber = topChassis.map(Chassis::getChassisNumber).orElse(null);

            validateChassisStockMessage = chassisRepository.validateChassisStock(businessUnitId, subBusinessUnitId, catalogIds.get(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS), chassisId, chassisNumber, input.getUserRegistrationId(), input.getLanguageId());
            if (!validateChassisStockMessage.trim().isBlank()) {
                return GateInGeneralRegisterV2Output.Output.builder()
                        .result(GateInGeneralRegisterV2Output.Result.builder()
                                .resultState(3)
                                .resultMessage(validateChassisStockMessage)
                                .build())
                        .build();

            }

            if (equipmentInput.isEmpty()) {
                ShippingLine dummyShippingLine = shippingLineRepository.findByShippingLine("OTHER").orElseThrow();
                Container notApplicableContainer = containerRepository.findByContainerNumber(CONTAINER_NOT_APPLICA);
                isOnlyChassis = true;

                input.setEquipmentCatEmptyFullId(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS));
                input.setMoveTypeId(catalogIds.get(CATALOG_GI_TYPE_MTY_DISCHARGE));
                gradeId = catalogIds.get(CATALOG_NO_GRADE);
                depotOperationId = catalogIds.get(CATALOG_DEPOT_OPER_MTY_DISCHARGE);

                if (notApplicableContainer == null) {
                    Optional<IsoCode> dummyIsoCode = isoCodeRepository.findByIsoCode(IS_ISO_CODE_DUMMY);
                    equipmentSizeId = dummyIsoCode.map(IsoCode::getCatSize).map(Catalog::getId).orElse(null);
                    equipmentTypeId = dummyIsoCode.map(IsoCode::getCatContainerType).map(Catalog::getId).orElse(null);
                    equipmentFamilyId = dummyIsoCode.map(IsoCode::getCatContainerType).map(Catalog::getVariable3).orElse(null);

                    notApplicableContainer = containerRepository.save(Container.builder()
                            .containerNumber(CONTAINER_NOT_APPLICA)
                            .catFamily(Optional.ofNullable(equipmentFamilyId).map(ef -> Catalog.builder().id(ef).build()).orElse(null))
                            .catSize(Optional.ofNullable(equipmentSizeId).map(es -> Catalog.builder().id(es).build()).orElse(null))
                            .catContainerType(Optional.ofNullable(equipmentTypeId).map(et -> Catalog.builder().id(et).build()).orElse(null))
                            .shippingLine(dummyShippingLine)
                            .containerTare(0)
                            .maximunPayload(0)
                            .isoCode(dummyIsoCode.orElse(null))
                            .catGrade(Catalog.builder().id(catalogIds.get(CATALOG_NO_GRADE)).build())
                            .shipperOwn(false)
                            .active(true)
                            .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                            .registrationDate(LocalDateTime.now())
                            .traceContainer("ins_gi_gral_chassis")
                            .catCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_GI_GENERAL_CREATION)).build())
                            .catEquipMeasureTare(Catalog.builder().id(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).build())
                            .catEquipMeasurePayload(Catalog.builder().id(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).build())
                            .build());
                }

                tare = notApplicableContainer.getContainerTare();
                catEquipMeasureTareId = Optional.ofNullable(notApplicableContainer).map(Container::getCatEquipMeasureTare).map(Catalog::getId).orElse(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS));
                payLoad = notApplicableContainer.getMaximunPayload();
                catEquipMeasurePayloadId = Optional.ofNullable(notApplicableContainer).map(Container::getCatEquipMeasurePayload).map(Catalog::getId).orElse(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS));
                isoCodeId = Optional.ofNullable(notApplicableContainer).map(Container::getIsoCode).map(IsoCode::getId).orElse(null);
                equipmentSizeId = Optional.ofNullable(notApplicableContainer).map(Container::getCatSize).map(Catalog::getId).orElse(null);
                equipmentTypeId = Optional.ofNullable(notApplicableContainer).map(Container::getCatContainerType).map(Catalog::getId).orElse(null);

                Vessel dummyVessel = vesselRepository.findByShip("VARIOUS").orElse(null);
                if (dummyVessel == null) {
                    dummyVessel = vesselRepository.save(Vessel.builder()
                            .ship(vessel)
                            .active(true)
                            .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                            .registrationDate(LocalDateTime.now())
                            .name(vessel)
                            .catVesselCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_GI_GENERAL_CREATION)).build())
                            .build());
                } else if (!dummyVessel.getActive()) {
                    dummyVessel.setActive(true);
                    dummyVessel.setModificationUser(User.builder().id(input.getUserRegistrationId()).build());
                    dummyVessel.setModificationDate(LocalDateTime.now());
                    dummyVessel.setCatVesselCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_GI_GENERAL_CREATION)).build());
                    dummyVessel.setName(vessel);
                    dummyVessel = vesselRepository.save(dummyVessel);
                }
                vesselId = dummyVessel.getId();

                VesselProgramming dummyVesselProgramming = vesselProgrammingRepository.getLastByVesselVoyageAndBusinessUnitId(pageable, vesselId, voyage, subBusinessUnitId).stream().findFirst().orElse(null);
                if (dummyVesselProgramming == null) {
                    dummyVesselProgramming = vesselProgrammingRepository.save(VesselProgramming.builder()
                            .businessUnit(BusinessUnit.builder().id(businessUnitId).build())
                            .subBusinessUnit(BusinessUnit.builder().id(subBusinessUnitId).build())
                            .vessel(dummyVessel)
                            .voyage(voyage)
                            .etaDate(LocalDateTime.now())
                            .etdDate(LocalDateTime.now())
                            .active(true)
                            .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                            .registrationDate(LocalDateTime.now())
                            .catCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_GI_GENERAL_CREATION)).build())
                            .build());
                }
                programacionNaveId = dummyVesselProgramming.getId();

                VesselProgrammingDetail dummyVesselProgrammingDetail = vesselProgrammingDetailRepository.getProgrammingDetailByProgrammingVesselAndDepotOperation(programacionNaveId, depotOperationId).orElse(null);
                if (dummyVesselProgrammingDetail == null) {
                    dummyVesselProgrammingDetail = vesselProgrammingDetailRepository.save(VesselProgrammingDetail.builder()
                            .vesselProgramming(dummyVesselProgramming)
                            .catOperation(Catalog.builder().id(depotOperationId).build())
                            .manifestYear(String.valueOf(LocalDateTime.now().getYear()))
                            .manifestNumber(null)
                            .active(true)
                            .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                            .registrationDate(LocalDateTime.now())
                            .catCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_GI_GENERAL_CREATION)).build())
                            .build());
                }

                Eir dummyEir = Eir.builder()
                        .businessUnit(BusinessUnit.builder().id(businessUnitId).build())
                        .subBusinessUnit(BusinessUnit.builder().id(subBusinessUnitId).build())
                        .localSubBusinessUnit(BusinessUnit.builder().id(input.getSubBusinessUnitLocalId()).build())
                        .catMovement(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS)).build())
                        .catEmptyFull(Catalog.builder().id(input.getEquipmentCatEmptyFullId()).build())
                        .catOrigin(Catalog.builder().id(input.getMoveTypeId()).build())
                        .truckArrivalDate(truckInDate)
                        .vesselProgrammingDetail(dummyVesselProgrammingDetail)
                        .container(notApplicableContainer)
                        .transportCompany(Company.builder().id(input.getTruckCompanyId()).build())
                        .truck(Truck.builder().id(input.getVehicleId()).build())
                        .driverPerson(Person.builder().id(input.getDriverId()).build())
                        .shippingLine(dummyShippingLine)
                        .taraCnt(tare)
                        .cargoMaximumCnt(payLoad)
                        .isoCode(Optional.ofNullable(isoCodeId).map(ic -> IsoCode.builder().id(ic).build()).orElse(null))
                        .catContainerType(Catalog.builder().id(equipmentTypeId).build())
                        .catSizeCnt(Catalog.builder().id(equipmentSizeId).build())
                        .catClassCnt(Catalog.builder().id(gradeId).build())
                        .observation(input.getComments())
                        .structureWithDamage(boxWithDamage)
                        .structureDamaged(boxDamaged)
                        .machineryWithDamage(machineryWithDamage)
                        .machineryDamaged(machineryDamaged)
                        .truckMultipleLoad(false)
                        .truckRetreatWithLoad(false)
                        .active(true)
                        .catCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_GI_GENERAL_CREATION)).build())
                        .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                        .registrationDate(LocalDateTime.now())
                        .clientCompany(null)
                        .traceEir("gi_gral_dummy")
                        .weightGoods(BigDecimal.ZERO)
                        .catMeasureWeight(null)
                        .catMeansTransport(Catalog.builder().id(catalogIds.get(CATALOG_MEANS_TRANSPORT)).build())
                        .catMeasureTare(Optional.ofNullable(catEquipMeasureTareId).map(emt -> Catalog.builder().id(emt).build()).orElse(null))
                        .catMeasurePayload(Optional.ofNullable(catEquipMeasurePayloadId).map(emp -> Catalog.builder().id(emp).build()).orElse(null))
                        .chassisNumber(input.getInputChassisNumber())
                        .flagChassisStayed(input.getFlagChassisStayed())
                        .flagOnSiteInspection(input.getInspectionSitu())
                        .numberTwr(input.getTwrNumber())
                        .build();

                dummyEir = eirRepository.save(dummyEir);
                equipmentInput = Arrays.asList(GateInGeneralRegisterV2Equipment.builder()
                        .equipmentId(notApplicableContainer.getId())
                        .equipmentNumber(CONTAINER_NOT_APPLICA)
                        .eirId(dummyEir.getId())
                        .build());
            }

            List<Chassis> chassisToRegister = new ArrayList<>();
            chassisToRegister.add(chassisRepository.findById(chassisInput.getChassisId()).orElse(null));
            chassisToRegister = chassisToRegister.stream().filter(Objects::nonNull).toList();
            Chassis selectedChassis = chassisToRegister.stream().findFirst().orElse(null); //SHOULD ALWAYS BE THERE
            chassisId = selectedChassis.getId();
            chassisNumber = selectedChassis.getChassisNumber();
            chassisTypeId = Optional.ofNullable(selectedChassis.getCatChassisType()).map(Catalog::getId).orElse(null);
            documentChassisDetailId = chassisInput.getDocumentChassisDetailId();

            EirChassis eirChassis = eirChassisRepository.save(EirChassis.builder()
                    .chassisDocumentDetail(Optional.ofNullable(documentChassisDetailId).map(dcc -> ChassisDocumentDetail.builder().id(dcc).build()).orElse(null))
                    .chassis(Chassis.builder().id(chassisId).build())
                    .active(true)
                    .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                    .registrationDate(LocalDateTime.now())
                    .traceEirChassis("gi_ins")
                    .build());
            eirChassisId = eirChassis.getId();

            eirChassisZoneRepository.chassisFirstActivityZone(eirChassisId, catalogIds.get(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS), chassisTypeId, input.getUserRegistrationId(), catalogIds.get(CATALOG_GI_GENERAL_CREATION));

            ChassisDocumentDetail chassisDocumentDetail = chassisDocumentDetailRepository.findById(documentChassisDetailId).orElse(null);
            if (chassisDocumentDetail != null) {
                chassisDocumentDetail.setCatStatusChassis(Catalog.builder().id(catalogIds.get(CATALOG_CHASSIS_IN_PROGRESS)).build());
                chassisDocumentDetail.setTraceChassisDocDetail("gi_updinprogress");
                chassisDocumentDetailRepository.save(chassisDocumentDetail);
            }
            selectedChassis.setCatChassisType(Catalog.builder().id(chassisTypeId).build());
            selectedChassis.setModificationUser(User.builder().id(input.getUserRegistrationId()).build());
            selectedChassis.setModificationDate(LocalDateTime.now());
            selectedChassis.setTraceChassis("gi_updchatype");
            chassisRepository.save(selectedChassis);

            for (GateInGeneralRegisterV2Equipment eq : equipmentInput) {
                eq.setEirChassisId(eirChassisId);
                eq.setChassisNumber(chassisNumber);
                eq.setEquipmentMessage(isOnlyChassis ? messageLanguageService.getMessage("PRC_GI_GENERAL", 2, input.getLanguageId()) : eq.getEquipmentMessage());

                Eir eirEq = eirRepository.findById(eq.getEirId()).orElse(null);
                if (eirEq != null) {
                    eirEq.setEirChassis(eirChassis);
                    eirEq.setTraceEir("gi_updeirchasis");
                    eirEq.setModificationUser(User.builder().id(input.getUserRegistrationId()).build());
                    eirEq.setModificationDate(LocalDateTime.now());
                    eirRepository.save(eirEq);
                }
            }

            List<StockChassis> stockChassisList = stockChassisRepository.getByEirChassisIdAndChassisIdAndActive(eirChassisId, chassisId, true);
            if (stockChassisList.isEmpty()) {
                stockChassisRepository.save(StockChassis.builder()
                        .subBusinessUnit(BusinessUnit.builder().id(subBusinessUnitId).build())
                        .chassis(Chassis.builder().id(chassisId).build())
                        .eirChassisGatein(EirChassis.builder().id(eirChassisId).build())
                        .inStockChassis(true)
                        .active(true)
                        .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                        .registrationDate(LocalDateTime.now())
                        .build());
            }
        }

        if (equipmentInput.stream().anyMatch(ei -> ei.getEirId() != null)) resultState = 1;

        if (resultState.equals(1) && noMaersk != null && noMaersk) {
            eirRepository.equipmentNoMaerskNotification(equipmentInput.reversed().stream().findFirst().map(GateInGeneralRegisterV2Equipment::getEirId).orElse(0));
        }

        //SDY INTEGRATION
        BusinessUnit localBusinessUnit = businessUnitRepository.findById(input.getSubBusinessUnitLocalId()).orElse(null);
        if (input.getSystemRuleId() != null && !input.getSystemRuleId().isBlank() && !equipmentInput.isEmpty() && localBusinessUnit != null) {
            SystemRule systemRule = systemRuleRepository.findByAliasAndActiveTrue(input.getSystemRuleId());
            Type integrationSystemRuleListType = new TypeToken<ArrayList<SystemRuleDTO>>() {
            }.getType();
            List<SystemRuleDTO> integrationSystemRuleList = gson.fromJson(systemRule.getRule(), integrationSystemRuleListType);
            typeProductIntegration = integrationSystemRuleList.stream().filter(sr -> sr.getSubBusinessUnitLocalAlias().equals(localBusinessUnit.getBusinesUnitAlias()) && sr.getTypeProcess().equals(input.getTypeProcess())).findFirst().map(SystemRuleDTO::getTypeProductIntegration).orElse(null);
        }

        GateInGeneralRegisterV2Output.Result result = GateInGeneralRegisterV2Output.Result.builder()
                .resultState(resultState)
                .resultMessage(validateChassisStockMessage)
                .eirId(eirChassisId != null ? eirChassisId.toString() : "")
                .build();

        List<GateInGeneralRegisterV2Output.Response> responses = new ArrayList<>();
        Catalog emptyFullCatalog = catalogRepository.findById(input.getEquipmentCatEmptyFullId()).orElse(null); //SHOULD ALWAYS BE THERE
        for (GateInGeneralRegisterV2Equipment eq : equipmentInput) {
            EmrInspection emrInspection = emrInspectionRepository.findByEirId(eq.getEirId()).orElse(null);
            EirChassisZoneActivity eirChassisZoneActivity = eirChassisZoneActivityRepository.findByEirChassisId(pageable, eq.getEirChassisId()).stream().findFirst().orElse(null);
            String isEmpty = input.getEquipmentCatEmptyFullId().equals(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS)) ? "1" : "0";
            responses.add(GateInGeneralRegisterV2Output.Response.builder()
                    .eirId(eq.getEirId())
                    .equipmentCatEmpty_fullAlias(emptyFullCatalog.getAlias())
                    .equipmentNumber(eq.getEquipmentNumber())
                    .eirChassisId(eq.getEirChassisId())
                    .chassisNumber(eq.getChassisNumber())
                    .statusMessage(eq.getEirId() != null ? "1" : "2")
                    .message(eq.getEquipmentMessage())
                    .emrInspectionId(emrInspection != null ? emrInspection.getId() : null)
                    .yardLocation(null)
                    .containerNotApplicable(eq.getEquipmentNumber().equals(CONTAINER_NOT_APPLICA) ? "1" : "0")
                    .eirChassisZoneActivityId(eirChassisZoneActivity != null ? eirChassisZoneActivity.getId().toString() : null)
                    .isEmpty(eq.getEquipmentNumber().equals(CONTAINER_NOT_APPLICA) ? null : isEmpty)
                    .build());
        }

        GateInGeneralRegisterV2Output.Integration integration = GateInGeneralRegisterV2Output.Integration.builder()
                .typeProductIntegration(typeProductIntegration)
                .integrationResult(null)
                .build();

        Object[] apsValidation = systemRuleRepository.gateinGeneralEquipmentAppointmentValidate(input.getSubBusinessUnitLocalId(), input.getEquipment())[0];
        GateInGeneralRegisterV2Output.Integration apsIntegration = GateInGeneralRegisterV2Output.Integration.builder()
                .integrationResult(apsValidation.length > 1 && apsValidation[1] != null ? apsValidation[1].toString() : null)
                .typeProductIntegration(apsValidation[0].toString())
                .build();

        return GateInGeneralRegisterV2Output.Output.builder()
                .result(result)
                .response(responses)
                .integration(integration)
                .apsIntegration(apsIntegration)
                .build();
    }

    public void handleIntegrations(GateInGeneralRegisterV2Input.Input input, GateInGeneralRegisterV2Output.Output output) throws Exception {
        GateInGeneralRegisterV2Output.Integration apsIntegration = output.getApsIntegration();
        if (apsIntegration != null && apsIntegration.getTypeProductIntegration() != null && apsIntegration.getIntegrationResult() != null && apsIntegration.getIntegrationResult().equals("aps")) {
            APSIntegrationConnection connection = new APSIntegrationConnection();
            connection.attendAppointmentAPS(input.getUserRegistrationId().toString(), input.getEquipment());
        }

        if (!input.getInspectionSitu() && output.getIntegration() != null
                && output.getIntegration().getTypeProductIntegration() != null
                && output.getIntegration().getTypeProductIntegration().equals("sdy")) {
            gateInYardIntegration(input, output);
        }
    }

    private void gateInYardIntegration(GateInGeneralRegisterV2Input.Input input, GateInGeneralRegisterV2Output.Output output) throws Exception {
        try {
            List<GateInGeneralRegisterV2Output.Response> responseList = output.getResponse();
            SD1IntegrationConnection connection = connectionFactory.createConnection(
                    env.getProperty("msk.api.apiUserLogin.sdy.loginUrl"),
                    env.getProperty("msk.api.apiUserLogin.sdy.user"),
                    env.getProperty("msk.api.apiUserLogin.sdy.password"),
                    env.getProperty("msk.api.apiUserLogin.sdy.system")
            );

            ArrayList<YardContainer> yardContainerList = new ArrayList<>();
            for (GateInGeneralRegisterV2Output.Response res : output.getResponse()) {
                yardContainerList.add(YardContainer.builder()
                        .containerNumber(res.getEquipmentNumber())
                        .eirNumber(res.getEirId())
                        .build());
            }

            YardGateInInput yardInput = YardGateInInput.builder()
                    .userId(input.getUserRegistrationId().toString())
                    .operationType(input.getOperationCode())
                    .businessUnitId(input.getSubBusinessUnitLocalId())
                    .containers(yardContainerList)
                    .build();

            HashMap<String, Object> resp = connection.post(env.getProperty("msk.api.apiUserLogin.sdy.gateInUrl"), yardInput.toJSON("SDY"));
            if (resp != null) {
                JSONObject containers = new JSONObject(resp);
                if (containers.get("result") instanceof JSONObject result) {
                    if (result.has("contenedores")) {
                        JSONArray listContainers = result.getJSONArray("contenedores");
                        for (int i = 0; i < listContainers.length(); i++) {
                            JSONObject objContainer = listContainers.getJSONObject(i);
                            String equipmentNumber = objContainer.getString("numero_contenedor");
                            JSONObject objLocationDestiny = objContainer.getJSONObject("ubicacion_destino");

                            String yardLocation = null;

                            if (Integer.parseInt(objContainer.get("tamanio").toString()) >= 40 && objLocationDestiny.getString("bloque_tipo").equals("STACK")) {
                                String oldRow = objLocationDestiny.getString("fila");
                                String oldRowLastCharacter = oldRow.substring(oldRow.length() - 1);
                                String oldRowFormatted = oldRow.length() == 1 ? "" : oldRow;
                                String newRow = oldRow.matches("\\d+") ? String.valueOf(Integer.parseInt(oldRow) + 1)
                                        : oldRowFormatted + Character.toString((char) (oldRowLastCharacter.charAt(0) + 1));
                                yardLocation = objLocationDestiny.get("bloque_codigo") + newRow + objLocationDestiny.getString("columna") + objLocationDestiny.get("nivel").toString();
                            } else {
                                yardLocation = objLocationDestiny.getString("ubicacion");
                            }

                            for (int e = 0; e < responseList.size(); e++) {
                                if (responseList.get(e).getEquipmentNumber().equals(equipmentNumber)) {
                                    responseList.get(e).setYardLocation(yardLocation);
                                }
                            }
                        }

                    } else if (result.has("error_message")) {
                        JSONObject errorResult = new JSONObject();
                        errorResult.put("sdy", result.toString());
                        output.getIntegration().setIntegrationResult(errorResult.toString());
                    }
                }
            }
        } catch (Exception e) {
            JSONObject errorResult = new JSONObject();
            JSONObject errorMessage = new JSONObject()
                    .put("error_message", "Error performing integration call: " + e.getMessage())
                    .put("activity_log_id", "0");
            errorResult.put("sdy", errorMessage);
            output.getIntegration().setIntegrationResult(errorResult.toString());
        }
    }
}