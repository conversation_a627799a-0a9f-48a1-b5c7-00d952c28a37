package com.maersk.sd1.sdg.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@JsonFormat(shape = JsonFormat.Shape.ARRAY)
public class GateoutGeneralDocumentFindOutputDTO {

    @Data
    public static class ResultState {
        @JsonProperty("result_state")
        private Integer resultState;

        @JsonProperty("result_message")
        private String resultMessage;
    }

    @Data
    public static class DocumentDetails {
        @JsonProperty("is_empty_full_id")
        private BigDecimal isEmptyFullId;

        @JsonProperty("empty_full_alias")
        private String emptyFullAlias;

        @JsonProperty("reference_document")
        private String referenceDocument;

        @JsonProperty("vesel_detail")
        private String veselDetail;

        @JsonProperty("operation_type")
        private String operationType;

        @JsonProperty("shipping_line")
        private String shippingLine;

        @JsonProperty("shipper_name")
        private String shipperName;

        @JsonProperty("consignee_name")
        private String consigneeName;

        @JsonProperty("equipment_id")
        private Integer equipmentId;

        @JsonProperty("equipment_number")
        private String equipmentNumber;

        @JsonProperty("equipment_size")
        private String equipmentSize;

        @JsonProperty("equipment_type")
        private String equipmentType;

        @JsonProperty("iso_code_id")
        private Integer isoCodeId;

        @JsonProperty("iso_code")
        private String isoCode;

        @JsonProperty("move_type_id")
        private BigDecimal moveTypeId;

        @JsonProperty("seal_1_mandatory")
        private Boolean seal1Mandatory;

        @JsonProperty("seal_2_mandatory")
        private Boolean seal2Mandatory;

        @JsonProperty("seal_3_mandatory")
        private Boolean seal3Mandatory;

        @JsonProperty("seal_4_mandatory")
        private Boolean seal4Mandatory;

        @JsonProperty("documento_carga_id")
        private Integer documentoCargaId;

        @JsonProperty("booking_id")
        private Integer bookingId;

        @JsonProperty("programacion_nave_detalle_id")
        private Integer programacionNaveDetalleId;

        @JsonProperty("documento_carga_detalle_id")
        private Integer documentoCargaDetalleId;

        @JsonProperty("mercaderia")
        private String mercaderia;

        @JsonProperty("item")
        private String item;

        @JsonProperty("depot_operation_alias")
        private String depotOperationAlias;
    }

    @Data
    public static class AppointmentDetails {
        @JsonProperty("equipment_number")
        private String equipmentNumber;

        @JsonProperty("aps_id")
        private String apsId;

        @JsonProperty("aps_external_value")
        private Character apsExternalValue;

        @JsonProperty("aps_external_code")
        private Character apsExternalCode;

        @JsonProperty("planning_detail_id")
        private Integer planningDetailId;

        @JsonProperty("etd")
        private String etd;

        @JsonProperty("truck_company_id")
        private Integer truckCompanyId;

        @JsonProperty("truck_company_name")
        private String truckCompanyName;
    }

    @Data
    public static class ApsData {
        @JsonProperty("appointmentId")
        private Long appointmentId;

        @JsonProperty("appointmentDate")
        private String appointmentDate;

        @JsonProperty("documentNumber")
        private String documentNumber;

        @JsonProperty("driverFullname")
        private String driverFullname;

        @JsonProperty("driverLicenseNumber")
        private String driverLicenseNumber;

        @JsonProperty("truckLicensePlate")
        private String truckLicensePlate;

        @JsonProperty("typeProcess")
        private String typeProcess;

        @JsonProperty("externalValue")
        private String externalValue;

        @JsonProperty("externalCode")
        private String externalCode;
    }

    @Data
    public static class GateoutGeneralAppointmentValidate {
        @JsonProperty("type_product_integration")
        private String typeProductIntegration;
        @JsonProperty("sub_business_unit_local_alias")
        private String subBusinessUnitLocalAlias;
    }

    @JsonProperty("result_state")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<ResultState> resultState;

    @JsonProperty("document_details")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<DocumentDetails> documentDetails;

    @JsonProperty("appointment_details")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<AppointmentDetails> appointmentDetails;

    @JsonProperty("aps_data")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<ApsData> apsData;

    @JsonProperty("gateout_general_appointment_validate")
    @JsonFormat(shape = JsonFormat.Shape.ARRAY)
    private List<GateoutGeneralAppointmentValidate> gateoutGeneralAppointmentValidate;
}