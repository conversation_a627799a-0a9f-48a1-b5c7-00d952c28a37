package com.maersk.sd1.sdg.service;

import com.maersk.sd1.common.Parameter;
import com.maersk.sd1.common.dto.CargoDocumentDetailReceiptDTO;
import com.maersk.sd1.common.dto.ContainerRestrictionDetailDTO;
import com.maersk.sd1.common.dto.EIRDTO;
import com.maersk.sd1.common.dto.SystemRuleMergedShippingLinesDTO;
import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.Container;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.ContainerRestrictionService;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.common.service.SystemRuleService;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sdg.controller.dto.SdggateoutGeneralAssignmentContainerFindInput;
import com.maersk.sd1.sdg.controller.dto.SdggateoutGeneralAssignmentContainerFindOutput;
import com.maersk.sd1.sdg.controller.dto.SdggateoutGeneralAssignmentContainerFindOutputResponse;
import com.maersk.sd1.sdg.controller.dto.SdggateoutGeneralAssignmentContainerFindOutputResult;
import com.maersk.sd1.sdg.dto.EIRContBooDocCargo;
import com.maersk.sd1.sdg.dto.GateoutGeneralAssignmentContainerFindBooking;
import com.maersk.sd1.sdg.repository.SdgEirRepository;
import com.maersk.sd1.sdg.repository.SdgStockEmptyRepository;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

@Service
@RequiredArgsConstructor
public class GateoutGeneralAssignmentContainerFindService {

    private static final String CNTX = "{CNTX}";
    private static final String PRC_GO_GENERAL = "PRC_GO_GENERAL";
    private static final String BKX = "{BKX}";
    private static final String PRC_GATE_OUT_EMPTY_LIGHT = "PRC_GATE_OUT_EMPTY_LIGHT";
    private static final String PRC_FULL_GI_GO = "PRC_FULL_GI_GO";
    private static final String GENERAL = "GENERAL";
    private final ContainerRepository containerRepository;
    private final SdgEirRepository sdgEirRepository;
    private final ContainerRestrictionService containerRestrictionService;
    private final ContainerPreassignmentRepository containerPreassignmentRepository;
    private final GESCatalogService gesCatalogService;
    private final MessageLanguageService messageLanguageService;
    private final SystemRuleService systemRuleService;
    private final BookingService bookingService;
    private final ShippingLineRepository shippingLineRepository;
    private final CargoDocumentDetailRepository cargoDocumentDetailRepository;
    private final TransportPlanningDetailRepository transportPlanningDetailRepository;
    private final IsoCodeRepository isoCodeRepository;
    private final StockEmptyRepository stockEmptyRepository;
    private final StockFullRepository stockFullRepository;
    private final CatalogRepository catalogRepository;
    private final CatalogLanguageRepository catalogLanguageRepository;
    private final SdgStockEmptyRepository sdgStockEmptyRepository;

    @Transactional
    public SdggateoutGeneralAssignmentContainerFindOutput execute(SdggateoutGeneralAssignmentContainerFindInput.Root inputRoot) {

        SdggateoutGeneralAssignmentContainerFindInput.Input input = inputRoot.getSdg().getInput();
        
        SdggateoutGeneralAssignmentContainerFindOutput output = new SdggateoutGeneralAssignmentContainerFindOutput();
        SdggateoutGeneralAssignmentContainerFindOutputResult result = new SdggateoutGeneralAssignmentContainerFindOutputResult();

        if(!Parameter.isValidContainer(input.getContainerNumber())){
            return output.mapResult(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY_LIGHT, 7, input.getLanguageId()));
        }

        List<String> catalogAliases = getCatalogAliases();

        HashMap<String, Integer> catalogs = gesCatalogService.findIdsByAliasesIn(catalogAliases);
        Integer isEmpty = catalogs.get(Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS);
        Integer isFull = catalogs.get(Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS);
        Container container = getContainer(input.getContainerNumber(), input.getLanguageId(), catalogs);

        if(container == null){
            return output.mapResult(messageLanguageService.getMessage(GENERAL, 19, input.getLanguageId(), Map.of(CNTX, input.getContainerNumber())));
        }

        if(Boolean.FALSE.equals(container.getActive())){
            return output.mapResult(messageLanguageService.getMessage(GENERAL, 20, input.getLanguageId(), Map.of(CNTX, input.getContainerNumber())));
        }
        String bookingNumber = null;
        Integer eirCatEmptyFullId =null;
        Boolean eirActive = false;
        Boolean bookingActive = false;
        Integer bookingGoutId = null;
        Integer bookingCatBookingStatusId = null;
        String cargoDocument = null;
        Boolean cargoDocumentActive = false;
        Short eirControlAssignmentLight = null;
        Integer eirContainerId = null;
        String containerNumber = null;
        Integer eirSubBusinessUnitId = null;
        String remarkRulesName = null;
        Integer eirDocumentCargoGofId = null;
        String eirSeal1 = null;
        String eirSeal2 = null;
        String eirSeal3 = null;
        String eirSeal4 = null;


        EIRContBooDocCargo eIRContBooDocCargo = sdgEirRepository.findContBooDocCargo(input.getEirId());

        if(Objects.nonNull(eIRContBooDocCargo)) {
            Integer containerNoCntId = containerRepository.findByContainerNumber(Parameter.CONTAINER_NO_CNT).getId();
            Integer containerNotApplicableId = containerRepository.findByContainerNumber(Parameter.CONTAINER_NOT_APPLICA).getId();
            Integer isContainer = catalogRepository.findIdByAlias(Parameter.EQUIPMENT_CATEGORY_CONTAINER);
            Integer isGoCustomDelivery = catalogRepository.findIdByAlias(Parameter.CATALOG_CONTAINER_MOVEMENT_CUSTOM_DELIVERY_ALIAS);
            Integer isGateOut = catalogRepository.findIdByAlias(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS);
            Integer containerId = container.getId();
            if (!Objects.equals(containerId, containerNoCntId) && !Objects.equals(containerId, containerNotApplicableId)
                && Objects.equals(isGateOut, eIRContBooDocCargo.getCatMovementId())) {
                Integer eirGateIn = sdgStockEmptyRepository.findGateInEirByContainerIdAndEirId(containerId, input.getEirId());

                if (eirGateIn == null) {
                    Optional<Integer> optionalEirGateIn = sdgStockEmptyRepository.findGateInEirByContainerIdAndSubBusinessUnitId(containerId, eIRContBooDocCargo.getEIRSubBusinessUnitId());
                    if (optionalEirGateIn.isPresent()) {
                        eirGateIn = optionalEirGateIn.get();
                    }
                }
                Integer catContainerStatus = sdgEirRepository.fn_GetEquipmentConditionID(eirGateIn, isContainer, "S", "CUR");
                String structureDamaged = (catContainerStatus == null || catContainerStatus == 0)
                        ? null
                        : catalogLanguageRepository.fnCatalogTranslationDescLong(catContainerStatus, input.getLanguageId());
                if (Objects.equals(eIRContBooDocCargo.getEIRCatOriginId(), isGoCustomDelivery)
                        && StringUtils.isNotBlank(structureDamaged) && structureDamaged.equalsIgnoreCase("Damaged")) {
                        return output.mapResult(messageLanguageService.getMessage("ASSIGNMENT_GATE_OUT", 8, input.getLanguageId()));
                }
            }
            bookingNumber = eIRContBooDocCargo.getBookingNumber();
            eirCatEmptyFullId = eIRContBooDocCargo.getEIRCatEmptyFullId();
            eirActive = eIRContBooDocCargo.getEIRActive();
            bookingActive = eIRContBooDocCargo.getBookingActive();
            bookingGoutId = eIRContBooDocCargo.getBookingGoutId();
            bookingCatBookingStatusId = eIRContBooDocCargo.getBookingCatBookingStatusId();
            cargoDocument = eIRContBooDocCargo.getCargoDocument();
            cargoDocumentActive = eIRContBooDocCargo.getCargoDocumentActive();
            eirControlAssignmentLight = eIRContBooDocCargo.getEIRControlAssignmentLight();
            eirContainerId = eIRContBooDocCargo.getEIRContainerId();
            containerNumber = eIRContBooDocCargo.getContainerNumber();
            eirSubBusinessUnitId = eIRContBooDocCargo.getEIRSubBusinessUnitId();
            remarkRulesName = eIRContBooDocCargo.getRemarkRulesName();
            eirDocumentCargoGofId = eIRContBooDocCargo.getEIRDocumentCargoGofId();
            eirSeal1 = eIRContBooDocCargo.getEIRSeal1();
            eirSeal2 = eIRContBooDocCargo.getEIRSeal2();
            eirSeal3 = eIRContBooDocCargo.getEIRSeal3();
            eirSeal4 = eIRContBooDocCargo.getEIRSeal4();
        }
        if ((Objects.equals(eirCatEmptyFullId, isEmpty) && bookingNumber == null) ||
                (Objects.equals(eirCatEmptyFullId, isFull) && cargoDocument == null)) {
            return output.mapResult(messageLanguageService.getMessage(GENERAL, 11, input.getLanguageId()));
        }

        if(Boolean.FALSE.equals(eirActive)) {
            return output.mapResult(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY_LIGHT, 9, input.getLanguageId()));
        }

        if(Boolean.FALSE.equals(bookingActive)){
            return output.mapResult(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY_LIGHT, 21, input.getLanguageId()));
        }

        if(bookingGoutId != null){
            if(Objects.equals(bookingCatBookingStatusId, catalogs.get(Parameter.CATALOG_DOCUMENT_CARGO_TYPE_STATUS_CANCELLED_ALIAS))){
                return output.mapResult(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY_LIGHT, 22, input.getLanguageId()));
            }

            if(Objects.equals(bookingCatBookingStatusId, catalogs.get(Parameter.CATALOG_DOCUMENT_CARGO_TYPE_STATUS_LOCKED_ALIAS))){
                return output.mapResult(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY_LIGHT, 23, input.getLanguageId()));
            }

        }

        if(cargoDocument != null && !cargoDocumentActive){
            return output.mapResult(messageLanguageService.getMessage(PRC_GO_GENERAL, 5, input.getLanguageId()));
        }

        if(eirControlAssignmentLight == 1 && !Objects.equals(eirContainerId, container.getId())){
            if(bookingGoutId != null) {
                return output.mapResult(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY_LIGHT, 10, input.getLanguageId(), Map.of(BKX, bookingNumber, CNTX, containerNumber)));
            } else {
                return output.mapResult(messageLanguageService.getMessage(PRC_GO_GENERAL, 8, input.getLanguageId(), Map.of("{DOCX}", cargoDocument, CNTX, containerNumber)));
            }
        }

        if(eirControlAssignmentLight == 1 && Objects.equals(eirContainerId, container.getId())){
            return output.mapResult(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY_LIGHT, 11, input.getLanguageId(), Map.of(CNTX, containerNumber)));
        }

        if(eirControlAssignmentLight == 3){
            if(bookingGoutId != null){
                return output.mapResult(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY_LIGHT, 12, input.getLanguageId(), Map.of(BKX, bookingNumber, CNTX, containerNumber)));
            } else {
                return output.mapResult(messageLanguageService.getMessage(PRC_GO_GENERAL, 9, input.getLanguageId(), Map.of("{DOCX}", cargoDocument, CNTX, containerNumber)));
            }
        }

        List<ContainerRestrictionDetailDTO> containerRestrictions = containerRestrictionService.getContainerRestrictionRepository().findRestrictions(container.getId(), eirSubBusinessUnitId, eirCatEmptyFullId);

        // Find restriction for Full process
            if(eirCatEmptyFullId.equals(isFull) && !CollectionUtils.isEmpty(containerRestrictions)) {
            return output.mapResult(messageLanguageService.getMessage(PRC_FULL_GI_GO, 5, input.getLanguageId(), Map.of(CNTX, ""+container.getId(),"{RSTX}", containerRestrictionService.getRestrictionDetailDescription(containerRestrictions))));
        }


            if(eirCatEmptyFullId.equals(isEmpty)){

            Optional<Integer> containerPreassignmentId = containerPreassignmentRepository.findPreAssigmentByPreAssingment(bookingGoutId, container.getId(), catalogs.get(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS), isEmpty);

            if(containerPreassignmentId.isEmpty()){

                containerPreassignmentId = containerPreassignmentRepository.findPreAssigmentByDocCargoDetail(bookingGoutId, container.getId(), catalogs.get(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS), isEmpty);

                if(containerPreassignmentId.isEmpty() && !containerRestrictions.isEmpty()) {

                    return output.mapResult(messageLanguageService.getMessage(PRC_FULL_GI_GO, 5, input.getLanguageId(), Map.of(CNTX, ""+container.getId(),"{RSTX}", containerRestrictionService.getRestrictionDetailDescription(containerRestrictions))));

                } else {

                    List<GateoutGeneralAssignmentContainerFindBooking> bookingsDetail = bookingService.findBookingDetailsByBookingId(bookingGoutId);

                    boolean existsPendings = bookingsDetail.stream().anyMatch(records -> records.getQuantityRequired() - records.getQuantityAssigned() > 0);

                    if(!existsPendings){
                        return output.mapResult(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY_LIGHT, 13, input.getLanguageId(), Map.of(BKX, bookingNumber)));
                    }

                    List<SystemRuleMergedShippingLinesDTO> mergedShippingLines = systemRuleService.mapRuleMergedShippingLines();

                    List<SystemRuleMergedShippingLinesDTO.MergedDetail> shippingLines = findShippingLines(bookingsDetail, mergedShippingLines);

                    if(!findFromShippingLines(container.getShippingLine().getId(), shippingLines)){

                        String shippingLineByCnt = shippingLineRepository.getNameById(container.getShippingLine().getId());
                        String shippingLineByBk = shippingLineRepository.getNameById(bookingsDetail.getFirst().getShippingLineId());

                        return output.mapResult(messageLanguageService.getMessage(PRC_GO_GENERAL, 6, input.getLanguageId(), Map.of("{LNC}", shippingLineByCnt,"{LNB}", shippingLineByBk)));

                    }

                    boolean bkBySizeType = bookingsDetail.stream()
                            .anyMatch(booking -> Objects.equals(booking.getCatSize(), container.getCatSize().getId()) && Objects.equals(booking.getCatContainerType(), container.getCatContainerType().getId()));

                    boolean bkBySizeTypeEmptyRemark = bookingsDetail.stream()
                            .anyMatch(booking -> Objects.equals(booking.getCatSize(), container.getCatSize().getId()) && Objects.equals(booking.getCatContainerType(), container.getCatContainerType().getId()) && booking.getRemarkRulesName().isEmpty());

                    boolean bkBySizeTypesRemark = bookingsDetail.stream()
                            .anyMatch(booking -> Objects.equals(booking.getCatSize(), container.getCatSize().getId()) && (Objects.equals(booking.getCatContainerType(), catalogs.get(Parameter.CATALOG_TYPE_CONTAINER_DRY_ALIAS)) || Objects.equals(booking.getCatContainerType(), catalogs.get(Parameter.CATALOG_TYPE_CONTAINER_HC_ALIAS))) && booking.getRemarkRulesName().equals(Parameter.RULE_REMARK_FLAGTOFLEX));
                    if(
                            (remarkRulesName.isEmpty() && !bkBySizeType) ||
                            (remarkRulesName.equals(Parameter.RULE_REMARK_FLAGTOFLEX) && (!Objects.equals(container.getCatContainerType().getId(), catalogs.get(Parameter.CATALOG_TYPE_CONTAINER_DRY_ALIAS)) && !Objects.equals(container.getCatContainerType().getId(), catalogs.get(Parameter.CATALOG_TYPE_CONTAINER_HC_ALIAS))) && !bkBySizeTypeEmptyRemark) ||
                            (remarkRulesName.equals(Parameter.RULE_REMARK_FLAGTOFLEX) && (Objects.equals(container.getCatContainerType().getId(), catalogs.get(Parameter.CATALOG_TYPE_CONTAINER_DRY_ALIAS)) || Objects.equals(container.getCatContainerType().getId(), catalogs.get(Parameter.CATALOG_TYPE_CONTAINER_HC_ALIAS))) && !bkBySizeTypesRemark)
                    ){
                        String sizeContainerDesc = gesCatalogService.getCatalogTranslationDesc(container.getCatSize().getId(), input.getLanguageId());
                        String typeContainerDesc = gesCatalogService.getCatalogTranslationDesc(container.getCatContainerType().getId(), input.getLanguageId());
                        return output.mapResult(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY_LIGHT, 15, input.getLanguageId(), Map.of(CNTX, input.getContainerNumber(),"{TNX}", sizeContainerDesc, "{TPX}", typeContainerDesc)));
                    }

                    boolean bkBySizeTypePayload = bookingsDetail.stream()
                            .anyMatch(booking -> Objects.equals(booking.getCatSize(), container.getCatSize().getId()) && Objects.equals(booking.getCatContainerType(), container.getCatContainerType().getId()) && booking.getMaximumLoadRequired()<= container.getMaximunPayload());

                    boolean bkBySizeTypePayloadRemarkEmpty = bookingsDetail.stream()
                            .anyMatch(booking -> Objects.equals(booking.getCatSize(), container.getCatSize().getId()) && Objects.equals(booking.getCatContainerType(), container.getCatContainerType().getId()) && booking.getMaximumLoadRequired()<= container.getMaximunPayload() && booking.getRemarkRulesName().isEmpty());

                    boolean bkBySizeTypesRemarkPayload = bookingsDetail.stream()
                            .anyMatch(booking -> Objects.equals(booking.getCatSize(), container.getCatSize().getId()) && (Objects.equals(booking.getCatContainerType(), catalogs.get(Parameter.CATALOG_TYPE_CONTAINER_DRY_ALIAS)) || Objects.equals(booking.getCatContainerType(), catalogs.get(Parameter.CATALOG_TYPE_CONTAINER_HC_ALIAS))) && booking.getMaximumLoadRequired()<= container.getMaximunPayload() && booking.getRemarkRulesName().equals(Parameter.RULE_REMARK_FLAGTOFLEX));

                    if(
                            (remarkRulesName.isEmpty() && !bkBySizeTypePayload) ||
                            (remarkRulesName.equals(Parameter.RULE_REMARK_FLAGTOFLEX) && (!Objects.equals(container.getCatContainerType().getId(), catalogs.get(Parameter.CATALOG_TYPE_CONTAINER_DRY_ALIAS)) && !Objects.equals(container.getCatContainerType().getId(), catalogs.get(Parameter.CATALOG_TYPE_CONTAINER_HC_ALIAS))) && !bkBySizeTypePayloadRemarkEmpty) ||
                            (remarkRulesName.equals(Parameter.RULE_REMARK_FLAGTOFLEX) && (Objects.equals(container.getCatContainerType().getId(), catalogs.get(Parameter.CATALOG_TYPE_CONTAINER_DRY_ALIAS)) || Objects.equals(container.getCatContainerType().getId(), catalogs.get(Parameter.CATALOG_TYPE_CONTAINER_HC_ALIAS))) && !bkBySizeTypesRemarkPayload)
                    ){

                        Integer maximunPayload = bookingsDetail.stream()
                                .filter(booking -> Objects.equals(booking.getCatSize(), container.getCatSize().getId()) &&
                                        Objects.equals(booking.getCatContainerType(), container.getCatContainerType().getId()))
                                .map(GateoutGeneralAssignmentContainerFindBooking::getMaximumLoadRequired)
                                .findFirst()
                                .orElse(null);

                        return output.mapResult(messageLanguageService.getMessage(PRC_GO_GENERAL, 7, input.getLanguageId(), Map.of("{PMRX}", ""+maximunPayload)));
                    }

                    String otherBookingNumber = containerPreassignmentRepository.findBookingNumberFromPreassigmentEIR(container.getId(), eirSubBusinessUnitId, catalogs.get(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS), isEmpty);

                    if(otherBookingNumber != null && !otherBookingNumber.isEmpty()){
                        return output.mapResult(messageLanguageService.getMessage(PRC_GATE_OUT_EMPTY_LIGHT, 19, input.getLanguageId(), Map.of(BKX, otherBookingNumber)));
                    }

                }

            }

        }

        CargoDocumentDetailReceiptDTO cargoDocumentDetailReceived = null;
        Integer transportPlanningDetailId = null;

        if(eirCatEmptyFullId.equals(isFull)) {

            List<Integer> cargoDocumentDetailIds = cargoDocumentDetailRepository.findIdByContainerId(container.getId());

            if(cargoDocumentDetailIds.isEmpty()){
                return output.mapResult(messageLanguageService.getMessage(PRC_GO_GENERAL, 10, input.getLanguageId(), Map.of(CNTX, container.getContainerNumber())));
            }

            cargoDocumentDetailReceived = cargoDocumentDetailRepository.findCargoDocumentDetailReceived(cargoDocumentDetailIds, container.getId(), eirDocumentCargoGofId, catalogs.get(Parameter.CATALOG_MEASURE_WEIGHT_KG_ALIAS));

            if(cargoDocumentDetailReceived.getCargoDocumentDetailId() == null){
                return output.mapResult(messageLanguageService.getMessage(PRC_GO_GENERAL, 11, input.getLanguageId(), Map.of(CNTX, container.getContainerNumber(),"{DCX}", cargoDocument)));
            }

            if(cargoDocumentDetailReceived.getReceivedQuantity() > 0 || cargoDocumentDetailReceived.getReceivedWeight().compareTo(BigDecimal.ZERO) > 0){
                return output.mapResult(messageLanguageService.getMessage(PRC_GO_GENERAL, 12, input.getLanguageId(), Map.of(CNTX, container.getContainerNumber())));
            }

            if(cargoDocumentDetailReceived.getBalanceQuantity() <= 0){
                return output.mapResult(messageLanguageService.getMessage(PRC_GO_GENERAL, 13, input.getLanguageId(), Map.of(CNTX, container.getContainerNumber())));
            }

            transportPlanningDetailId = transportPlanningDetailRepository.findTransportPlanningDetailIdByMoveTypeAndTrkPlanning(cargoDocumentDetailReceived.getCargoDocumentDetailId(), catalogs.get(Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS), catalogs.get(Parameter.CATALOG_TRANSPORT_PLANNING_STATUS_SCHEDULED_ALIAS));

            if(transportPlanningDetailId == null){
                return output.mapResult(messageLanguageService.getMessage(PRC_FULL_GI_GO, 11, input.getLanguageId(), Map.of(CNTX, container.getContainerNumber())));
            }

        }

        result.setResultState(1);
        result.setResultMessage("");
        result.setRestrictionContainerId(1);

        Integer gateInEIR = null;

        EIRDTO eirDto = sdgEirRepository.findEirDetailsById(input.getEirId());

        if(eirDto.getCatMovementDescription().equals(Parameter.CATALOG_TYPE_MOVEMENT_PREFIX_INPUT)){
            gateInEIR = eirDto.getEirId();
        }

        if(eirDto.getCatMovementDescription().equals(Parameter.CATALOG_TYPE_MOVEMENT_PREFIX_OUTPUT) && eirDto
                .getCatEmptyFullDescription().equals(Parameter.CATALOG_TYPE_PROCESS_PREFIX_EMPTY)){
            gateInEIR = stockEmptyRepository.findEirOutIdByEirInIdAndContainerId(eirDto.getEirId(), eirDto.getContainerId());
        }

        if(eirDto.getCatMovementDescription().equals(Parameter.CATALOG_TYPE_MOVEMENT_PREFIX_OUTPUT) && eirDto
                .getCatEmptyFullDescription().equals(Parameter.CATALOG_TYPE_PROCESS_PREFIX_FULL)){
            gateInEIR = stockFullRepository.findEirInIdByEirOutIdAndContainerId(eirDto.getEirId(), eirDto.getContainerId());
        }

        SdggateoutGeneralAssignmentContainerFindOutputResponse response = new SdggateoutGeneralAssignmentContainerFindOutputResponse();

        response.setContainerId(container.getId());
        response.setContainerNumber(container.getContainerNumber());
        response.setContainerType(gesCatalogService.getCatalogTranslationDesc(container.getCatSize().getId(), input.getLanguageId())+" "+gesCatalogService.getCatalogTranslationLongDesc(container.getCatContainerType().getId(), input.getLanguageId()));
        response.setIsoCode(isoCodeRepository.findCodeByIsoCodeId(container.getIsoCode().getId()));
        response.setGrade(gesCatalogService.getCatalogTranslationDesc(container.getCatGrade().getId(), input.getLanguageId()));
        response.setTara(container.getValueTare());
        response.setPayload(Parameter.formatNumber(container.getMaximunPayload())+" "+gesCatalogService.getCatalogTranslationDesc(container.getCatEquipMeasurePayload().getId(), input.getLanguageId()));
        response.setReeferType(Optional.ofNullable(container.getCatReeferType()).map(cat -> gesCatalogService.getCatalogTranslationDesc(cat.getId(), input.getLanguageId())).orElse(null));
        response.setDocumentoCargaDetalleId(Optional.ofNullable(cargoDocumentDetailReceived).map(CargoDocumentDetailReceiptDTO::getCargoDocumentDetailId).orElse(null));

        if(cargoDocumentDetailReceived != null && cargoDocumentDetailReceived.getCargoDocumentDetailId() != null){
            response.setReceivedWeight(Parameter.formatNumber(container.getMaximunPayload())+" "+gesCatalogService.getCatalogTranslationDesc(container.getCatEquipMeasurePayload().getId(), input.getLanguageId()));
        } else {
            response.setReceivedWeight("");
        }
        response.setPlanningDetailId(transportPlanningDetailId);

        response.setSeal1(eirSeal1);
        response.setSeal2(eirSeal2);
        response.setSeal3(eirSeal3);
        response.setSeal4(eirSeal4);

        response.setStructureCondition(gesCatalogService.getCatalogTranslationLongDesc(sdgEirRepository.getEquipmentConditionId(gateInEIR, catalogs.get(Parameter.EQUIPMENT_CATEGORY_CONTAINER), "S", "CUR"), input.getLanguageId()));
        response.setMachineryCondition(gesCatalogService.getCatalogTranslationLongDesc(sdgEirRepository.getEquipmentConditionId(gateInEIR, catalogs.get(Parameter.EQUIPMENT_CATEGORY_CONTAINER), "M", "CUR"), input.getLanguageId()));

        output.setResult(List.of(result));
        output.setResponse(List.of(response));

        return output;

    }

    private List<String> getCatalogAliases() {
        return List.of(
                Parameter.CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS,
                Parameter.CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS,
                Parameter.CATALOG_TYPE_PROCESS_IS_FULL_ALIAS,
                Parameter.CATALOG_DOCUMENT_CARGO_TYPE_STATUS_ACTIVE_ALIAS,
                Parameter.CATALOG_DOCUMENT_CARGO_TYPE_STATUS_CANCELLED_ALIAS,
                Parameter.CATALOG_DOCUMENT_CARGO_TYPE_STATUS_LOCKED_ALIAS,
                Parameter.CATALOG_DOCUMENT_CREATION_ORIGIN_GATE_OUT_AUTOMATIC_ALIAS,
                Parameter.CATALOG_CONTAINER_MOVEMENT_POSITIONING_ALIAS,
                Parameter.CATALOG_CONTAINER_MOVEMENT_EVACUATION_ALIAS,
                Parameter.CATALOG_CONTAINER_MOVEMENT_CUSTOM_DELIVERY_ALIAS,
                Parameter.CATALOG_CONTAINER_MOVEMENT_SALE_ALIAS,
                Parameter.CATALOG_MEASURE_WEIGHT_KG_ALIAS,
                Parameter.CATALOG_TRANSPORT_PLANNING_STATUS_SCHEDULED_ALIAS,
                Parameter.EQUIPMENT_CATEGORY_CONTAINER,
                Parameter.CATALOG_PRE_ALLOCATED_ORIGIN_PRE_ALLOCATED_ALIAS,
                Parameter.GENERAL_CONDITION_EQUIPMENT_EMPTY_OK,
                Parameter.CATALOG_TYPE_CONTAINER_DRY_ALIAS,
                Parameter.CATALOG_TYPE_CONTAINER_HC_ALIAS
        );
    }

    private List<SystemRuleMergedShippingLinesDTO.MergedDetail> findShippingLines(List<GateoutGeneralAssignmentContainerFindBooking> bookingsDetail, List<SystemRuleMergedShippingLinesDTO> mergedShippingLines){

        // Find merged shipping lines
        for (GateoutGeneralAssignmentContainerFindBooking gateoutGeneralAssignmentContainerFindBooking : bookingsDetail) {

            List<SystemRuleMergedShippingLinesDTO.MergedDetail> findedGroup = findMergedShippingLines(gateoutGeneralAssignmentContainerFindBooking.getShippingLineId(), mergedShippingLines);
            if (!CollectionUtils.isEmpty(findedGroup)) {
                return findedGroup;
            }

        }

        // If not define a unique value from Booking
        List<SystemRuleMergedShippingLinesDTO.MergedDetail> newList = new ArrayList<>();

        SystemRuleMergedShippingLinesDTO.MergedDetail newDetail = new SystemRuleMergedShippingLinesDTO.MergedDetail();

        newDetail.setLineId(bookingsDetail.getFirst().getShippingLineId());

        newList.add(newDetail);

        return newList;

    }

    private List<SystemRuleMergedShippingLinesDTO.MergedDetail> findMergedShippingLines(Integer shippingLineId, List<SystemRuleMergedShippingLinesDTO> mergedShippingLines){

        for (SystemRuleMergedShippingLinesDTO mergedShippingLine : mergedShippingLines) {

            for (int k = 0; k < mergedShippingLine.getMergedDetails().size(); k++) {

                if (mergedShippingLine.getMergedDetails().get(k).getLineId() == shippingLineId) {

                    return mergedShippingLine.getMergedDetails();

                }

            }
        }
        return Collections.emptyList();

    }

    private boolean findFromShippingLines(Integer shippingLineId, List<SystemRuleMergedShippingLinesDTO.MergedDetail> mergedShippingLines){

        for (SystemRuleMergedShippingLinesDTO.MergedDetail mergedShippingLine : mergedShippingLines) {

            if (mergedShippingLine.getLineId() == shippingLineId) {

                return true;

            }

        }

        return false;

    }

    private Container getContainer(String containerNumber, Integer languageId, HashMap<String, Integer> catalogs){

        Container container = containerRepository.findByContainerNumber(containerNumber);

        if(container != null){

            Integer catMeasureWeightId = container.getCatEquipMeasurePayload() != null && container.getCatEquipMeasurePayload().getId() != null?container.getCatEquipMeasurePayload().getId():catalogs.get(Parameter.CATALOG_MEASURE_WEIGHT_KG_ALIAS);

            if(container.getCatEquipMeasurePayload() == null){
                container.setCatEquipMeasurePayload(new Catalog());
            }

            container.getCatEquipMeasurePayload().setId(catMeasureWeightId);
            container.setValueTare(Parameter.formatNumber(container.getContainerTare())+" "+gesCatalogService.getCatalogTranslationDesc(catMeasureWeightId, languageId));

        }

        return container;

    }

}
