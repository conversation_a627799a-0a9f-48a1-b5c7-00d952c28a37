package com.maersk.sd1.common;

import lombok.experimental.UtilityClass;

import java.text.DecimalFormat;
import java.util.List;

@UtilityClass
public class Parameter {

    // Related to user
    public static final Integer DEFAULT_USER_ID_MASTER = 1;

    // Ralated to container
    public static final String CONTAINER_NO_CNT = "NO-CNT";
    public static final String CONTAINER_NOT_APPLICA = "NOT APPLICA";

    // Values related to container
    public static final Integer CONTCONTAINER_NO_CNT_VALUE = 950;
    public static final Integer CONTCONTAINER_NO_APPLICA_VALUE = 147503;

    // Related to catalog
    public static final String CATALOG_TYPE_GATE_IS_GATEIN_ALIAS = "43080"; // Old catalogTypeGateIsGateInAlias
    public static final String CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS = "43081"; // Old catalogTypeGateIsGateOutAlias
    public static final String CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS = "43083"; // Old catalogTypeProcessIsEmptyAlias
    public static final String CATALOG_TYPE_PROCESS_IS_FULL_ALIAS = "43084"; // Old catalogTypeProcessIsFullAlias
    public static final String CATALOG_DOCUMENT_CARGO_TYPE_STATUS_ACTIVE_ALIAS = "43061";
    public static final String CATALOG_DOCUMENT_CARGO_TYPE_STATUS_CANCELLED_ALIAS = "43062";
    public static final String CATALOG_DOCUMENT_CARGO_TYPE_STATUS_LOCKED_ALIAS = "43063";
    public static final String CATALOG_DOCUMENT_CREATION_ORIGIN_GATE_OUT_AUTOMATIC_ALIAS = "sd1_creationsource_general_gateout_aut";
    public static final String CATALOG_CONTAINER_MOVEMENT_POSITIONING_ALIAS = "43105";
    public static final String CATALOG_CONTAINER_MOVEMENT_EVACUATION_ALIAS = "43095";
    public static final String CATALOG_CONTAINER_MOVEMENT_CUSTOM_DELIVERY_ALIAS = "43097";
    public static final String CATALOG_CONTAINER_MOVEMENT_SALE_ALIAS = "43099";
    public static final String CATALOG_MEASURE_WEIGHT_KG_ALIAS = "cat_43011_kg";
    public static final String CATALOG_TRANSPORT_PLANNING_STATUS_SCHEDULED_ALIAS = "cat_48746_programm";
    public static final String CATALOG_PRE_ALLOCATED_ORIGIN_PRE_ALLOCATED_ALIAS = "47747";
    public static final String CATALOG_TYPE_CONTAINER_DRY_ALIAS = "31049";
    public static final String CATALOG_TYPE_CONTAINER_HC_ALIAS = "31053";
    public static final String CATALOG_CONFIGURATION_BUSINESS_UNIT_TIME_ZONE_ALIAS = "20251";
    public static final String CATALOG_CONFIGURATION_BUSINESS_UNIT_FORMAT_DATE_ALIAS = "48162";
    public static final String CATALOG_CONFIGURATION_BUSINESS_UNIT_FORMAT_DATE_TIME_ALIAS = "48163";
    public static final String CATALOG_TYPE_CREATION_SOURCE_GENERAL_GATEOUT_ALIAS = "sd1_creationsource_general_gateout";
    public static final String CATALOG_TYPE_CAT_GOE_LIGHT_ALIAS = "cat_42989_goe_light";
    public static final String CATALOG_BOX_INSPECTION_ALIAS = "cat_43161_box_inspection";
    public static final String CATALOG_ESTIMATE_BOX_ALIAS = "47490";
    public static final String CATALOG_CHASSIS_ZONE_ACTIVITY_INSPECTION_ALIAS = "sd1_chassis_zoneactiv_insp";
    public static final Integer ACTIVE_STATUS = 20253;
    public static final Integer CAT_ORIGIN = 42990;

    public static final String CATALOG_TYPE_MOVEMENT_PREFIX_INPUT = "I";
    public static final String CATALOG_TYPE_MOVEMENT_PREFIX_OUTPUT = "S";
    public static final String CATALOG_TYPE_PROCESS_PREFIX_EMPTY = "E";
    public static final String CATALOG_TYPE_PROCESS_PREFIX_FULL = "F";

    public static final String CATALOG_CHASSIS_DOCUMENT_PENDING = "sd1_chassisdoc_status_Pending";
    public static final String POSITIONING_ALIAS = "43094";
    public static final String RELOCATION_ALIAS = "42905";
    // Related to System Rule
    public static final String RULE_ALIAS_MERGED_SHIPPING_INES = "sds_merged_shipping_lines";

    // Related to Remark Rule
    public static final String RULE_REMARK_FLAGTOFLEX = "FLAG_TO_FLEX";

    public static final String EQUIPMENT_CATEGORY_CONTAINER = "sd1_equipment_category_container";
    public static final String EQUIPMENT_CATEGORY_CHASSIS = "sd1_equipment_category_chassis";
    public static final String EQUIPMENT_CATEGORY_GENSET = "sd1_equipment_category_genset"; // Old CATALOG_EQUIPMENT_CATEGORY_GENSET_ALIAS
    public static final String EQUIPMENT_CATEGORY_UNDERSLUNG = "sd1_equipment_category_underslung"; // Old CATALOG_EQUIPMENT_CATEGORY_UNDERSLUNG_ALIAS
    public static final String EXECUTED_MOVEMENT_INSTRUCTION_STATUS_ALIAS = "42896";
    public static final String CANCELLED_MOVEMENT_INSTRUCTION_STATUS_ALIAS = "42900";
    public static final String PENDING_MOVEMENT_INSTRUCTION_STATUS_ALIAS = "42897";

    public static final String IS_CREATION_GO_GENERAL = "sd1_creationsource_general_gateout";
    public static final String IS_CREATION_GO_LIGHT = "cat_42989_goe_light";

    public static final String CONTENT_TYPE_FULL = "43084";

    public static final String IS_DOCUMENT_ACTIVE = "43061";

    public static final String IS_TYPE_BOOKING = "cat_48734_bk";

    public static final String IS_OPER_IMPORT = "cat_48734_bk";
    public static final String IS_OPER_EXPORT = "cat_48789_export";
    public static final String IS_OPER_STORAGE = "cat_48789_storage";

    public static final String IS_EIR_MANUAL = "cat_42989_eir_manual";
    public static final String IS_PLANNING_PENDING = "cat_48746_programm";
    public static final String IS_PLANNING_IN_PROCESS = "cat_48746_inprocess ";
    public static final String IS_PLANNING_DONE = "cat_48746_done";
    public static final String IS_DOC_CHASSIS_COMPLETED = "sd1_chassisdoc_status_completed";

    public static final String IS_OPERATIVE_CHASSIS = "sd1_chassis_structurecondition_ok";
    public static final String IS_OPERATIVE_DIRTY_CONTAINER = "sd1_gral_condition_mtycontainer_in_cleaning";
    public static final String STATUS_INSPECTION_FULL_PENDING = "cat_57749_pending";
    public static final String STATUS_INSPECTION_FULL_COMPLETED = "cat_57749_completed";
    public static final String STATUS_INSPECTION_FULL_CANCELED = "cat_57749_canceled";

    public static final String CONDITION_EQUIPMENT_FULL_INSP = "sd1_full_box_machinery_condition_nc";
    public static final String CONDITION_EQUIPMENT_FULL_OK = "sd1_full_box_machinery_condition_ok";
    public static final String CONDITION_EQUIPMENT_FULL_DAM = "sd1_full_box_machinery_condition_dam";

    public static final String GENERAL_CONDITION_EQUIPMENT_EMPTY_INSP = "sd1_gral_condition_mtycontainer_in_inspection";
    public static final String GENERAL_CONDITION_EQUIPMENT_EMPTY_DAM = "sd1_gral_condition_mtycontainer_damaged";
    public static final String GENERAL_CONDITION_EQUIPMENT_EMPTY_REP = "sd1_gral_condition_mtycontainer_in_repair";
    public static final String GENERAL_CONDITION_EQUIPMENT_EMPTY_OD = "sd1_gral_condition_mtycontainer_in_cleaning";
    public static final String GENERAL_CONDITION_EQUIPMENT_EMPTY_OW = "sd1_gral_condition_mtycontainer_ok_wet";
    public static final String GENERAL_CONDITION_EQUIPMENT_EMPTY_OK = "sd1_gral_condition_mtycontainer_ok";

    public static final String STRUCTURE_CONDITION_EQUIPMENT_EMPTY_SC = "48219";
    public static final String STRUCTURE_CONDITION_EQUIPMENT_EMPTY_DD = "48220";
    public static final String STRUCTURE_CONDITION_EQUIPMENT_EMPTY_DM = "48221";
    public static final String STRUCTURE_CONDITION_EQUIPMENT_EMPTY_DH = "48222";
    public static final String STRUCTURE_CONDITION_EQUIPMENT_EMPTY_OS = "48223";
    public static final String STRUCTURE_CONDITION_EQUIPMENT_EMPTY_OW = "48224";
    public static final String STRUCTURE_CONDITION_EQUIPMENT_EMPTY_OH = "48225";
    public static final String STRUCTURE_CONDITION_EQUIPMENT_EMPTY_OK = "48226";

    public static final String ACTIVITY_ZONE_EMPTY_INSP = "cat_43161_box_inspection";
    public static final String ACTIVITY_ZONE_EMPTY_PTI = "cat_43161_pti";
    public static final String ACTIVITY_ZONE_EMPTY_REP = "cat_43161_box_repair";
    public static final String ACTIVITY_ZONE_EMPTY_REPM = "cat_43161_machinery_repair";
    public static final String ACTIVITY_ZONE_EMPTY_LAV = "cat_43161_wash";
    public static final String ACTIVITY_ZONE_EMPTY_DD = "cat_43161_damaged_box";
    public static final String ACTIVITY_ZONE_EMPTY_DM = "cat_43161_damaged_machinery";
    public static final String ACTIVITY_ZONE_EMPTY_OK = "cat_43161_good_condition";

    public static final String ACTIVITY_ZONE_CONTAINER_EMPTY_SINP = "cat_43170_pending_inspect";
    public static final String ACTIVITY_ZONE_CONTAINER_EMPTY_PTI = "cat_43170_zone_pti";
    public static final String ACTIVITY_ZONE_CONTAINER_EMPTY_DD = "cat_43170_damaged_box";
    public static final String ACTIVITY_ZONE_CONTAINER_EMPTY_DM = "cat_43170_damaged_machinery";
    public static final String ACTIVITY_ZONE_CONTAINER_EMPTY_REP = "cat_43170_box_repair";
    public static final String ACTIVITY_ZONE_CONTAINER_EMPTY_REPM = "cat_43170_machinery_repair";
    public static final String ACTIVITY_ZONE_CONTAINER_EMPTY_LAV = "cat_43170_washed";
    public static final String ACTIVITY_ZONE_CONTAINER_EMPTY_OK = "cat_43170_good_condition";
    public static final String ACTIVITY_ZONE_CONTAINER_EMPTY_VEN = "cat_43170_sale";
    public static final String ACTIVITY_ZONE_CONTAINER_EMPTY_DRCH = "cat_43170_rejected";

    public static final String ESTIMATE_CREATED_STATUS = "47547";
    public static final String ESTIMATE_FINALIZED_STATUS = "47484";

    public static final String STATUS_REPAIR_EMPTY_CONTAINER_PENDING = "47507";
    public static final String STATUS_REPAIR_EMPTY_CONTAINER_APPROVED = "47508";
    public static final String STATUS_REPAIR_EMPTY_CONTAINER_REJECTED = "47509";
    public static final String STATUS_REPAIR_EMPTY_CONTAINER_REPAIRED = "sd1_repair_approval_status_repaired";
    public static final String STATUS_REPAIR_EMPTY_CONTAINER_REPAIRED_PROGRESS = "sd1_repair_approval_status_in_progress";

    public static final String STATUS_CLEANING_EMPTY_CONTAINER_CLEANED = "sd1_cleanning_status_cleaned";
    public static final String STATUS_CLEANING_EMPTY_CONTAINER_PENDING = "sd1_repair_approval_status_pending";
    public static final String STATUS_CLEANING_EMPTY_CONTAINER_DIRTY = "sd1_repair_approval_status_operative_dirty";
    public static final String STATUS_CLEANING_EMPTY_CONTAINER_WET = "sd1_repair_approval_status_operative_wet";
    public static final String STATUS_CLEANING_EMPTY_CONTAINER_CLEANING_PROGRESS = "sd1_repair_approval_status_cleanning_in_progress";

    public static final String STRUCTURE_CONDITION_CHASSIS_INSP = "sd1_chassis_structurecondition_nc";
    public static final String STRUCTURE_CONDITION_CHASSIS_OK = "sd1_chassis_structurecondition_ok";
    public static final String STRUCTURE_CONDITION_CHASSIS_DAM = "sd1_chassis_structurecondition_dam";

    public static final String ZONE_ACTIVITY_CHASSIS_CHINSP = "sd1_chassis_zoneactiv_insp";
    public static final String ZONE_ACTIVITY_CHASSIS_CHREP = "sd1_chassis_zoneactiv_rep";
    public static final String ZONE_ACTIVITY_CHASSIS_CHOK = "sd1_chassis_zoneactiv_ok";
    public static final String ZONE_ACTIVITY_CHASSIS_CHDAM = "sd1_chassis_zoneactiv_dam";
    public static final String EIR_NOTIFICATION_PROCESSING = "sd1_eir_notification_processing";
    public static final String SD1_EIR_NOTIFICATION_ALIAS = "sd1_eir_notification";
    public static final String SD1_EIR_NOTIFICATION_PENDING_EMAIL_ALIAS = "sd1_eir_notification_pending_email";

    // ID
    public static final String SD1_RULE_INETRATION_APS = "sd1_rule_intetration_aps";

    public static final String SD1_CHASSIS_OPETYPE_CUSDELIVERY = "sd1_chassis_opetype_cusdelivery";
    public static final String SD1_CHASSISDOC_REFTYPE_BK = "sd1_chassisdoc_reftype_bK";
    public static final String SD1_CREATIONSOURCE_CHASSISDOC = "sd1_creationsource_chassisdoc";

    // ALIAS
    public static final String SUB_BUSINESS_UNIT_LOCAL_ALIAS = "sub_business_unit_local_alias";
    public static final String EXCLUDE_SHIPPING_LINES = "exclude_shipping_lines";
    public static final String TYPE_PROCESS = "type_process";
    public static final String IS_CHASSIS = "sd1_equipment_category_chassis";
    public static final String IS_CONTAINER = "sd1_equipment_category_container";
    public static final String IS_GENSET = "sd1_equipment_category_genset";
    public static final String IS_UNDERSLUNG = "sd1_equipment_category_underslung";
    public static final String IS_DOCUMENT_TYPE_BK = "cat_48734_bk";
    public static final String IS_DOCUMENT_TYPE_BL = "cat_48734_bl";
    public static final String IS_OPERATION_EXPORT = "cat_48789_export";
    public static final String IS_OPERATION_IMPORT = "cat_48789_import";
    public static final String IS_OPERATION_STORAGE = "cat_48789_storage";
    public static final String IS_CATALOGO_SCAC = "ges_company_configuration_scac";
    public static final String STATUS_IN_PROGRESS_ALIAS = "sd1_chassisdoc_status_inprogress";
    public static final String STATUS_PENDING_ALIAS = "sd1_chassisdoc_status_pending";

    // Related to Booking
    public static final String BKEDI_PENDING_ALIAS = "48271";
    public static final String BKEDI_TOPROCESS_ALIAS = "48272";
    public static final String BKEDI_DONE_ALIAS = "48273";
    public static final String BKEDI_REJECTED_ALIAS = "48274";
    public static final String BKEDI_NOTVALID_ALIAS = "48275";
    public static final String BKEDI_REJECTED_DUPL_ALIAS = "48276";
    public static final Integer REF_COPARN_CATEGORY_ID = 47743;
    public static final Integer BOOKING_CREATION_ORIGIN_CATEGORY_ID = 47739;
    public static final Integer REEFER_CONTROLLED_ATMOSPHERE = 31048;

    public static final String IS_STATE_TO_READ = "48271";
    public static final String IS_STATE_TO_PROCESS = "48272";
    public static final Integer ESTIMATE_STATUS_CREATED = 47547;
    public static final Integer ESTIMATE_STATUS_FINALIZED = 47484;
    public static final Integer ESTIMATE_TYPE_STRUCTURE = 47490;

    public static final String TRACK_PLANNING = "cat_42989_trkplanning";
    public static final String EQUIPMENT_GROUP_TYPE = "cat_48751_container";
    public static final String GRADE_NO_CLASS = "42922";
    public static final String SIMPLE_STORAGE = "cat_42992_simple_storage";
    public static final String DISCHARGE_FULL = "cat_42992_discharge_full";
    public static final String LOAD_FULL = "cat_42992_load_full";
    public static final String CONDITION_FCL = "43004";
    public static final String CONDITION_MTY = "43007";

    public static final String EIR_NOTIFICATION_PENDING = "sd1_eir_notification_pending";
    public static final String INSPECTION_GRAL_STATUS_PENDING = "sd1_statusinspectiongral_pending";
    public static final String INSPECTION_GRAL_STATUS_STARTED = "sd1_statusinspectiongral_started";
    public static final String INSPECTION_GRAL_STATUS_FINISHED = "sd1_statusinspectiongral_finished";
    public static final String CATALOG_ESTIMATE_TYPE_MACHINERY = "47491";

    public static final String IS_BKEDI_PENDING = "48271";
    public static final String IS_BKEDI_TO_PROCESS = "48272";
    public static final String IS_BKEDI_DONE = "48273";
    public static final String IS_BKEDI_REJECTED = "48274";
    public static final String IS_BKEDI_NOT_VALID = "48275";
    public static final String IS_BKEDI_REJECTED_DUPLICATE = "48276";
    public static final String IS_CUSTOMER_ROLE_ID = "cr_sd1_client";
    public static final String IS_BOOKING_TYPE_UPDATE = "sd1_bkedi_reference_replace";
    public static final String CATALOG_IS_EDI_MESSAGE_TYPE_COPARN = "sd1_bkedi_message_type_coparn";
    public static final String CATALOG_IS_EDI_MESSAGE_TYPE_301 = "sd1_bkedi_message_type_301";

    public static final String CATALOG_MEANS_TRANSPORT = "cat_48729_truck";
    public static final String CATALOG_NO_GRADE = "42922";
    public static final String CATALOG_PENDING_INSPECTION = "cat_57749_pending";
    public static final String CATALOG_DEPOT_OPER_MTY_DISCHARGE = "cat_42992_discharge_mty";
    public static final String CATALOG_GI_TYPE_MTY_DISCHARGE = "43087";
    public static final String CATALOG_GI_GENERAL_CREATION = "sd1_creationsource_general_gatein";
    public static final String CATALOG_CUSTOMER_RETURN = "43086";
    public static final String CATALOG_CHASSIS_IN_PROGRESS = "sd1_chassisdoc_status_inprogress";
    public static final String CATALOG_TRK_PLAN_IN_PROCESS = "cat_48746_inprocess";
    public static final String CATALOG_CHASSIS_IN_PENDING_ALIAS = "sd1_chassisdoc_status_pending";
    public static final String CATALOG_DEPOT_OPER_MTY_LOAD_ALIAS = "cat_42992_load_mty";
    public static final String CATALOG_SIZE_CONTAINER_20_ALIAS = "31070";
    public static final String CATALOG_SIZE_CONTAINER_40_ALIAS = "31071";
    public static final String CATALOG_CNT_FAMILY_DRY_ALIAS = "42882";
    public static final String CATALOG_CNT_TYPE_FLAT_ALIAS = "31051";
    public static final String CATALOG_CNT_TYPE_FLAT_RACK_ALIAS = "41977";
    public static final String CATALOG_CNT_TYPE_PLATFORM_ALIAS = "31059";
    public static final String CATALOG_DOC_TYPE_BL_ALIAS = "cat_48734_bl";
    public static final String CATALOG_CHASSIS_OPETYPE_CUSDELIVERY_ALIAS = "sd1_chassis_opetype_cusdelivery";

    public static final String SD1_BKEDI_MESSAGE_TYPE_COPARN = "sd1_bkedi_message_type_coparn";
    public static final String SD1_BKEDI_MESSAGE_TYPE_301 = "sd1_bkedi_message_type_301";

    public static final String CAT_42989_AUTO_COPARN = "cat_42989_auto_coparn";
    public static final String SD1_TEMPERAUREUNIT_CELSIUS = "sd1_temperatureunit_celsius";
    public static final String SD1_TEMPERATUREUNIT_FAHRENHEIT = "sd1_temperatureunit_fahrenheit";

    public static final String CHASSIS_ESTIMATE_SIZE_REPAIR = "sd1_chaestim_sizerepair";

    public static final String CHASSIS_ESTIMATE_STATUS_SUBMITTED = "sd1_chaest_submitted";
    public static final String CHASSIS_ESTIMATE_STATUS_APPROVED = "sd1_chaest_approved";
    public static final String CHASSIS_ESTIMATE_STATUS_FINALIZED = "sd1_chaest_finalized";
    public static final String CHASSIS_ESTIMATE_STATUS_CANCELED = "sd1_chaest_canceled";

    public static final String COSTA_RICA_BUSINESSUNIT_ALIAS = "CRI";

    public static final String CATALOG_SIMPLE_DEPOSIT = "43058";
    public static final String CATALOG_CONTAINER_PACKAGING = "43009";
    public static final String CATALOG_FCL_EMPTY_MEASUREMENT_UNIT = "cat_43013_unit";
    public static final String CATALOG_LCL_BB_MEASUREMENT_UNIT = "cat_43013_packages";
    public static final String CATALOG_PRIMARY_CARGO_ORIGIN = "43057";
    public static final String CATALOG_SEAWAY_TRANSPORT = "cat_43064_maritime";
    public static final String CATALOG_BL_MANUAL_CREATION_ORIGIN = "48243";
    public static final String CATALOG_BL_AUTOMATIC_CREATION_ORIGIN = "48355";
    public static final String CATALOG_RECEPTION_ORIGIN_VARIOUS = "48249";
    public static final String CATALOG_CREATION_FROM_REGISTER_BL = "48248";

    public static final String ESTIMATE_REJECTED = "47486";

    public static final String ESTIMATE_FINALIZED = "47484";
    public static final String ESTIMATE_CREATED = "47547";

    public static final String CHA_ESTIMATE_REJECTED = "sd1_chaest_rejected";
    public static final String CHA_ESTIMATE_FINALIZED = "sd1_chaest_finalized";
    public static final String CHA_ESTIMATE_CREATED = "sd1_chaest_created";
    public static final String CHA_ESTIMATE_CANCELLED = "sd1_chaest_canceled";
    public static final String CHA_ESTIMATE_REPAIR_CANCEL_CLONING = "sd1_estimate_repair_cancel_cloning";

    public static final String ESTIMATE_TRANSMISSION_PENDING = "47493";

    public static final String SD1_ACLOST_DISABLED = "sd1_aclost_disabled";

    public static final String IS_STATUS_PENDING_SEND = "43193";
    public static final String IS_SEND_FOR_EMAIL = "43186";
    public static final String CATALOG_MEASURE_WEIGHT_TON_ALIAS = "cat_43011_ton";
    public static final String CATALOG_MEASURE_WEIGHT_LB_ALIAS = "cat_43011_pound";
    public static final String IS_CODECO_FORMAT = "sd1_messagetype_gatetrans_codeco";
    public static final String IS_322_FORMAT = "sd1_messagetype_gatetrans_322";
    public static final String IS_TELEX_FORMAT = "sd1_messagetype_gatetrans_telex";

    public static final String CHA_ESTIMATE_SIZEREPAIR = "sd1_chaestim_sizerepair";

    public static final String CHASSIS_REPAPPROVAL_PENDING = "sd1_chassis_repapproval_pending";

    public static final String ESTIMATE_STATUS_REPAIR_COMPLETE = "47487";

    public static final String IS_REPAIR_STRUCTURE = "48030";
    public static final String IS_REPAIR_MACHINERY = "48031";
    public static final String ESTIMATE_REPAIR_CANCEL_OTHER = "sd1_estimate_repair_cancel_other";
    public static final String REASON_EMR_REJECTION = "47501";
    public static final String CATALOG_CONTAINER_TYPE = "sd1_container_type";

    public static final String CATALOG_CONTAINER_CREATION_SOURCE_LOAD_MASTER = "sd1_creationsource_loadmastercnt";
    public static final String CATALOG_CONTAINER_CREATION_SOURCE_FLEET_EDI = "sd1_creationsource_fleetequipedi";
    public static final String CATALOG_CONTAINER_UPDATE_SOURCE_FLEET_EDI = "sd1_updatesource_fleetequipedi";

    public static boolean isValidContainer(String containerNumber) {
        return containerNumber != null && !containerNumber.isEmpty() && !containerNumber.equals(CONTAINER_NO_CNT) && !containerNumber.equals(CONTAINER_NOT_APPLICA);
    }

    public static String formatNumber(Integer value, String format) {
        DecimalFormat formatDecimal = new DecimalFormat(format);
        return formatDecimal.format(value);
    }

    public static String formatNumber(Integer value) {
        return formatNumber(value, "0.0");
    }

    public static List<String> getAssignmentGateoutDeleteCatalogParameters() {
        return List.of(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS, CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS, CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS,
                CATALOG_TYPE_PROCESS_IS_FULL_ALIAS, CATALOG_TYPE_CREATION_SOURCE_GENERAL_GATEOUT_ALIAS,
                CATALOG_TYPE_CAT_GOE_LIGHT_ALIAS, CATALOG_TRANSPORT_PLANNING_STATUS_SCHEDULED_ALIAS);
    }

    public static List<String> getTruckDeparturePrintTicketCatalogAliases() {
        return List.of(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS, CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS, CATALOG_TYPE_PROCESS_IS_FULL_ALIAS,
                CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS, EQUIPMENT_CATEGORY_CHASSIS, EQUIPMENT_CATEGORY_CONTAINER,
                EQUIPMENT_CATEGORY_GENSET, EQUIPMENT_CATEGORY_UNDERSLUNG, CATALOG_BOX_INSPECTION_ALIAS,
                CATALOG_ESTIMATE_BOX_ALIAS, CATALOG_CHASSIS_ZONE_ACTIVITY_INSPECTION_ALIAS);
    }

}