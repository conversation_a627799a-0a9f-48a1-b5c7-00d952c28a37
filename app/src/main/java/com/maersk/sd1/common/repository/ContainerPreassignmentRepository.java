package com.maersk.sd1.common.repository;


import com.maersk.sd1.common.model.ContainerPreassignment;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

public interface ContainerPreassignmentRepository extends JpaRepository<ContainerPreassignment, Integer> {
    @Query("select c from ContainerPreassignment c where c.cargoDocumentDetail.id = :cargoDocumentDetailId and c.active = true")
    ContainerPreassignment findByCargoDocumentDetailIdAndActiveTrue(@Param("cargoDocumentDetailId") Integer cargoDocumentDetailId);

    @Query("""
            select c from ContainerPreassignment c
            where c.bookingDetail.id = :bookingDetailId and c.container.id = :containerId and c.active = true
            order by c.registrationDate DESC""")
    ContainerPreassignment findByBookingDetailIdAndContainerIdAndActiveTrueOrderByRegistrationDateDesc(@Param("bookingDetailId") Integer bookingDetailId, @Param("containerId") Integer containerId);

    @Query("SELECT p.id FROM ContainerPreassignment p " +
            "JOIN p.bookingDetail bd " +
            "JOIN bd.booking b " +
            "WHERE b.id = :bookingId " +
            "AND p.container.id = :containerId " +
            "AND NOT EXISTS ( " +
            "    SELECT 1 FROM EirDocumentCargoDetail edcd " +
            "    JOIN edcd.eir e " +
            "    WHERE edcd.id = p.cargoDocumentDetail.id " +
            "    AND e.catMovement.id = :isGateOut " +
            "    AND e.catEmptyFull.id = :isEmpty " +
            "    AND edcd.active = true " +
            "    AND e.active = true " +
            ") " +
            "AND b.active = true " +
            "AND bd.active = true " +
            "AND p.active = true " +
            "ORDER BY p.registrationDate DESC")
    Optional<Integer> findPreAssigmentByPreAssingment(@Param("bookingId") Integer bookingId,
                                                      @Param("containerId") Integer containerId,
                                                      @Param("isGateOut") Integer isGateOut,
                                                      @Param("isEmpty") Integer isEmpty);

    @Query("SELECT p.id FROM ContainerPreassignment p " +
            "JOIN p.bookingDetail bd " +
            "JOIN bd.booking b " +
            "JOIN CargoDocumentDetail dcd ON p.container.id = dcd.container.id AND p.bookingDetail.id = dcd.bookingDetail.id " +
            "WHERE b.id = :bookingId " +
            "AND p.container.id = :containerId " +
            "AND NOT EXISTS ( " +
            "    SELECT 1 FROM EirDocumentCargoDetail edcd " +
            "    JOIN edcd.eir e " +
            "    WHERE edcd.cargoDocumentDetail.id = dcd.id " +
            "    AND e.catMovement.id = :isGateOut " +
            "    AND e.catEmptyFull.id = :isEmpty " +
            "    AND edcd.active = true " +
            "    AND e.active = true " +
            ") " +
            "AND b.active = true " +
            "AND bd.active = true " +
            "AND p.active = true " +
            "AND dcd.active = true " +
            "ORDER BY p.registrationDate DESC")
    Optional<Integer> findPreAssigmentByDocCargoDetail(@Param("bookingId") Integer bookingId,
                                                       @Param("containerId") Integer containerId,
                                                       @Param("isGateOut") Integer isGateOut,
                                                       @Param("isEmpty") Integer isEmpty);

    @Query("SELECT b.bookingNumber FROM ContainerPreassignment p " +
            "JOIN p.bookingDetail bd " +
            "JOIN bd.booking b " +
            "LEFT JOIN Eir e ON p.container.id = e.container.id " +
            "AND e.subBusinessUnit.id = b.subBusinessUnit.id " +
            "AND e.vesselProgrammingDetail.id = b.vesselProgrammingDetail.id " +
            "AND e.catMovement.id = :isGateOut " +
            "AND e.catEmptyFull.id = :isEmpty " +
            "AND e.active = true " +
            "WHERE p.container.id = :containerId " +
            "AND b.subBusinessUnit.id = :subBusinessUnitId " +
            "AND e.id IS NULL " +
            "AND p.active = true " +
            "AND bd.active = true " +
            "AND b.active = true")
    String findBookingNumberFromPreassigmentEIR(@Param("containerId") Integer containerId,
                                                @Param("subBusinessUnitId") Integer subBusinessUnitId,
                                                @Param("isGateOut") Integer isGateOut,
                                                @Param("isEmpty") Integer isEmpty);

    @Query("SELECT p FROM ContainerPreassignment p WHERE p.container.id = :containerId AND p.active = true")
    List<ContainerPreassignment> findByContainerId(@Param("containerId") Integer containerId);


    @Query(value = "SELECT C FROM ContainerPreassignment C " +
            "WHERE C.cargoDocumentDetail.id = :cargoDocumentDetailId " +
            "AND C.catOriginPreassignment.id = :isPreallocation " +
            "AND C.active = TRUE " +
            "ORDER BY C.registrationDate DESC")
    List<ContainerPreassignment> findByCargoDocumentDetailIdAndCatOriginPreassignment(Integer cargoDocumentDetailId, Integer isPreallocation);


    @Query("SELECT cp FROM ContainerPreassignment cp WHERE cp.container.id IN :containerIds AND cp.active = true")
    List<ContainerPreassignment> findByContainerIdsAndActive(@Param("containerIds") List<Integer> containerIds);


    @Query("SELECT COUNT(p) FROM ContainerPreassignment p "
            + "WHERE p.bookingDetail.id = :bookingDetailId "
            + "  AND p.active = true")
    long countActivePreassignmentsByBookingDetail(@Param("bookingDetailId") Integer bookingDetailId);

    @Query("SELECT p FROM ContainerPreassignment p WHERE p.container.id = :containerNumber AND p.bookingDetail.booking.id = :booking AND p.active = true")
    List<ContainerPreassignment> findActivePreassignmentsByContainerAndBooking(
            @Param("containerNumber") Integer containerNumber,
            @Param("booking") Integer booking);

    @Query("SELECT p FROM ContainerPreassignment p WHERE p.container.id = :containerNumber AND p.bookingDetail.booking.id != :booking AND p.active = true")
    List<ContainerPreassignment> findActivePreassignmentsByContainerAndNotBooking(
            @Param("containerNumber") Integer containerNumber,
            @Param("booking") Integer booking);


    List<ContainerPreassignment> findByBookingDetailIdAndActiveOrderByRegistrationDateAsc(Integer bookingDetailId, Boolean active);

    @Query("SELECT cp FROM ContainerPreassignment cp WHERE cp.bookingDetail.id = :bookingDetailId AND cp.container.id = :containerId AND cp.active = true")
    Optional<ContainerPreassignment> findActiveByBookingIdAndContainerId(Integer bookingDetailId, Integer containerId);

    @Modifying
    @Query("""
            UPDATE ContainerPreassignment cp
            SET cp.active = false,
            cp.modificationUser.id = :modificationUserId,
            cp.modificationDate = CURRENT_TIMESTAMP,
            cp.tracePreassignment = :trace
            WHERE cp.bookingDetail.id = :bookingDetailId
            AND cp.container.id = :containerId 
            AND cp.active = true
            """)
    void unassignByBookingIdAndContainerId(Integer bookingDetailId, Integer containerId, Integer modificationUserId, String trace);

    @Query("""
            SELECT CASE WHEN count(cp) > 0 THEN true ELSE false END
            FROM ContainerPreassignment cp
            WHERE cp.bookingDetail.id = :bookingDetailId AND cp.container.id = :containerId
            AND cp.active = true 
            """)
    boolean existsActiveByBookingDetailContainer(Integer bookingDetailId, Integer containerId);

    @Modifying
    @Query("""
            UPDATE ContainerPreassignment cp
            SET cp.active = false,
            cp.modificationUser.id = :modificationUserId,
            cp.modificationDate = CURRENT_TIMESTAMP,
            cp.tracePreassignment = :trace
            WHERE cp.bookingDetail.id = :bookingDetailId
            AND cp.container.id = :containerId
            AND cp.active = true
            """)
    void disableByBookingDetailAndContainerId(Integer bookingDetailId, Integer containerId, Integer modificationUserId, String trace);
    
    @Query("SELECT COUNT(pc.id) FROM ContainerPreassignment pc "
            + "WHERE pc.active = true AND pc.bookingDetail.id = :bookingDetailId")
    long countActivePreassignments(@Param("bookingDetailId") Integer bookingDetailId);

    @Query("SELECT pc FROM ContainerPreassignment pc "
            + "JOIN pc.bookingDetail bd "
            + "JOIN bd.booking b "
            + "WHERE pc.active = true AND bd.active = true AND b.active = true "
            + "  AND pc.container.id = :containerId")
    List<ContainerPreassignment> findActivePreassignmentsForContainer(@Param("containerId") Integer containerId);



    @Query("SELECT p.id FROM ContainerPreassignment p " +
            "JOIN p.bookingDetail bd " +
            "JOIN bd.booking b " +
            "WHERE b.id = :bookingId " +
            "AND p.container.id = :containerId " +
            "AND NOT EXISTS ( " +
            "    SELECT 1 FROM Eir e " +
            "    JOIN EirDocumentCargoDetail edcd " +
            "    WHERE edcd.cargoDocumentDetail.id = p.cargoDocumentDetail.id " +
            "    AND e.catMovement = :isGateOut " +
            "    AND e.catEmptyFull = :isEmpty " +
            "    AND edcd.active = true " +
            "    AND e.active = true " +
            ") " +
            "AND b.active = true " +
            "AND bd.active = true " +
            "AND p.active = true " +
            "ORDER BY p.registrationDate DESC")
    Optional<Integer> findPreAssigmentByBookingIdAndContainerId(@Param("bookingId") Integer bookingId,
                                                                @Param("containerId") Integer containerId,
                                                                @Param("isGateOut") Integer isGateOut,
                                                                @Param("isEmpty") Integer isEmpty);

    @Query("""
            SELECT p 
            FROM ContainerPreassignment p 
            WHERE p.container.id IN :containerIds 
            AND p.active = true 
            AND p.cargoDocumentDetail.cargoDocument.id = :cargoDocumentId
            """)
    List<ContainerPreassignment> findByContainerIds(@Param("containerIds") List<Integer> containerIds, @Param("cargoDocumentId") Integer cargoDocumentId);

    @Query("SELECT cp FROM ContainerPreassignment cp " +
            "JOIN cp.container c " +
            "JOIN cp.bookingDetail bd " +
            "JOIN bd.booking b " +
            "WHERE cp.active = true AND b.active = true AND bd.active = true " +
            "AND cp.container.id = :containerId")
    List<ContainerPreassignment> findActivePreassignmentsByContainerId(@Param("containerId") Integer containerId);

    @Query("""
            SELECT cp FROM ContainerPreassignment cp
            LEFT JOIN cp.cargoDocumentDetail cdd
            LEFT JOIN EirDocumentCargoDetail edcd ON (edcd.cargoDocumentDetail.id = cdd.id AND edcd.active = true)
            LEFT JOIN edcd.eir eir ON (eir.id = edcd.eir.id AND eir.active = true AND eir.catMovement.id = :movementTypeId AND eir.catEmptyFull.id = :categoryId) 
            WHERE cp.bookingDetail.booking.id = :bookingId
            AND cp.container.id = :containerId
            AND eir IS NULL            
            AND cp.bookingDetail.booking.active = true
            AND cp.bookingDetail.active = true
            AND cp.active = true
            ORDER BY cp.registrationDate DESC
            """)
    List<ContainerPreassignment> findFirstActivePreassignmentByBookingAndContainerOptionOne(Pageable pageable, Integer bookingId, Integer containerId, Integer movementTypeId, Integer categoryId);

    @Query("""
            SELECT cp FROM ContainerPreassignment cp
            JOIN CargoDocumentDetail cdd ON (cdd.container.id = cp.container.id AND cdd.bookingDetail.id = cp.bookingDetail.id)            
            LEFT JOIN EirDocumentCargoDetail edcd ON (edcd.cargoDocumentDetail.id = cdd.id AND edcd.active = true)
            LEFT JOIN edcd.eir eir ON (eir.id = edcd.eir.id AND eir.active = true AND eir.catMovement.id = :movementTypeId AND eir.catEmptyFull.id = :categoryId) 
            WHERE cp.bookingDetail.booking.id = :bookingId
            AND cp.container.id = :containerId
            AND eir IS NULL            
            AND cp.bookingDetail.booking.active = true
            AND cp.bookingDetail.active = true
            AND cp.active = true
            AND cdd.active = true
            ORDER BY cp.registrationDate DESC
            """)
    List<ContainerPreassignment> findFirstActivePreassignmentByBookingAndContainerOptionTwo(Pageable pageable, Integer bookingId, Integer containerId, Integer movementTypeId, Integer categoryId);

    @Query("""
            SELECT cp FROM ContainerPreassignment cp
            WHERE cp.bookingDetail.id IN :bookingIdList
            AND cp.active = :active
            """)
    List<ContainerPreassignment> findAllByBookingDetailIdAndActive(List<Integer> bookingIdList, boolean active);
}