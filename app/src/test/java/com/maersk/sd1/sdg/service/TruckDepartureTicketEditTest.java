package com.maersk.sd1.sdg.service;

import com.maersk.sd1.common.model.Catalog;
import com.maersk.sd1.common.model.Eir;
import com.maersk.sd1.common.model.EirNotification;
import com.maersk.sd1.common.repository.CatalogRepository;
import com.maersk.sd1.common.repository.EirDocumentCargoDetailRepository;
import com.maersk.sd1.common.repository.EirNotificationRepository;
import com.maersk.sd1.sdg.controller.dto.SdgTruckDepartureTicketEditInput;
import com.maersk.sd1.sdg.controller.dto.SdgTruckDepartureTicketEditOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

class TruckDepartureTicketEditTest {
    @Mock
    private EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;

    @Mock
    private EirNotificationRepository eirNotificationRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private TruckDepartureTicketNotificationService truckDepartureTicketNotificationService;

    @InjectMocks
    private TruckDepartureTicketEditService truckDepartureTicketEditService;

    private final SdgTruckDepartureTicketEditInput root = new SdgTruckDepartureTicketEditInput();


    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        root.setEirNotificationId(1);
        root.setAzureStorageUrl("www.test.com/test");


    }

    @Test
    void getTruckDepartureTicketEdit_NullInput() {
        SdgTruckDepartureTicketEditOutput response;
        Catalog mockCatalog = new Catalog();
        when(catalogRepository.findByAlias(anyString())).thenReturn(mockCatalog);
        //null ppo
        response = truckDepartureTicketEditService.editTicket(null);
        assertNull(response);
    }

    @Test
    void getTruckDepartureTicketEdit_EirNotificationNotFound() {

        SdgTruckDepartureTicketEditOutput response;
        Catalog mockCatalog = new Catalog();
        when(catalogRepository.findByAlias(anyString())).thenReturn(mockCatalog);
        when(eirNotificationRepository.findById(anyInt())).thenReturn(Optional.empty());
        response = truckDepartureTicketEditService.editTicket(root);
        assertNull(response);
    }

    @Test
    void getTruckDepartureTicketEdit_EirNotificationFound() {

        SdgTruckDepartureTicketEditOutput response;
        Catalog mockCatalog = new Catalog();
        when(catalogRepository.findByAlias(anyString())).thenReturn(mockCatalog);

        Eir mockeir = new Eir();

        EirNotification mockEirNotification = new EirNotification();
        mockEirNotification.setEir(mockeir);
        Optional<EirNotification> optionalEirNotification = Optional.of(mockEirNotification);
        when(eirNotificationRepository.findById(anyInt())).thenReturn(optionalEirNotification);


        response = truckDepartureTicketEditService.editTicket(root);
        verify(eirNotificationRepository, times(1)).save(mockEirNotification);
        assertNotNull(response);
    }
}