package com.maersk.sd1.sdg.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdg.dto.ResponseTruckDepartureDelete;
import com.maersk.sd1.sdg.dto.TruckDepartureDeleteInput;
import com.maersk.sd1.sdg.repository.SdgEirRepository;
import com.maersk.sd1.sdg.repository.SdgEstimateEmrRepository;
import com.maersk.sd1.sdy.service.CheckYardIntegrationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;

class TruckDepartureDeleteServiceTest {
    @Mock
    private ObjectMapper objectMapper;
    @Mock
    private CatalogRepository catalogRepository;
    @Mock
    private SdgEirRepository eirRepository;
    @Mock
    private MessageLanguageRepository messageLanguageRepository;
    @Mock
    private ContainerRepository containerRepository;
    @Mock
    private UserRepository userRepository;
    @Mock
    private CatalogLanguageRepository catalogLanguageRepository;
    @Mock
    private BusinessUnitRepository businessUnitRepository;
    @Mock
    private SdgEstimateEmrRepository sdgEstimateEmrRepository;
    @Mock
    private ChassisEstimateRepository chassisEstimateRepository;
    @Mock
    private CargoDocumentDetailRepository cargoDocumentDetailRepository;
    @Mock
    private EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;
    @Mock
    private ContainerPreassignmentRepository containerPreassignmentRepository;
    @Mock
    private VesselProgrammingContainerRepository vesselProgrammingContainerRepository;
    @Mock
    private BookingDetailRepository bookingDetailRepository;
    @Mock
    private TransportPlanningDetailRepository transportPlanningDetailRepository;
    @Mock
    private EstimateEmrDetailRepository estimateEmrDetailRepository;
    @Mock
    private ChassisDocumentDetailRepository chassisDocumentDetailRepository;
    @Mock
    private ActivityLogRepository activityLogRepository;
    @Mock
    private SystemRuleRepository systemRuleRepository;
    @Mock
    private EirChassisRepository eirChassisRepository;
    @Mock
    private ChassisEstimateDetailRepository chassisEstimateDetailRepository;
    @Mock
    private ChassisBookingDocumentRepository chassisBookingDocumentRepository;
    @Mock
    private CheckYardIntegrationService checkYardIntegrationService;
    @InjectMocks
    private TruckDepartureDeleteService truckDepartureDeleteService;

    TruckDepartureDeleteInput.Input ppo;

    private static Catalog catEirGateIn;
    private static Catalog catEirGateOut;
    private static Catalog catEirEmpty;
    private static Catalog catEirFull;
    private static Catalog catRandom;
    private static Catalog catOriginPreAssignment;

    private static Eir mockEir;
    private static User mockUserModification;
    private static BusinessUnit mockSubBusinessUnitLocal;
    private static EstimateEmr mockEstimateEmr;
    private static ChassisEstimate mockChassisEstimate;
    private static EirDocumentCargoDetail mockEirDocumentCargoDetail;
    private static CargoDocumentDetail mockCargoDocumentDetail;


    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        ppo = new TruckDepartureDeleteInput.Input();

        ppo.setEirId(10);
        ppo.setSubBusinessUnitLocalId(30);
        ppo.setIdiomaId(1);
        ppo.setUsuarioModificacionId(20);
    }

    void setupMainParameters() {
        mockEir = new Eir();
        mockUserModification = new User();
        mockSubBusinessUnitLocal = new BusinessUnit();

        mockEir.setId(10);
        mockUserModification.setId(20);
        mockSubBusinessUnitLocal.setId(30);
        mockSubBusinessUnitLocal.setBusinesUnitAlias("randomAlias");
        mockEir.setLocalSubBusinessUnit(mockSubBusinessUnitLocal);

        when(eirRepository.findOneById(10)).thenReturn(mockEir);
        when(userRepository.findById(20)).thenReturn(Optional.of(mockUserModification));
        when(businessUnitRepository.findById(30)).thenReturn(Optional.of(mockSubBusinessUnitLocal));
    }

    void setupCatalogs() {
        catEirGateIn = new Catalog();
        catEirGateIn.setId(1);
        catEirGateOut = new Catalog();
        catEirGateOut.setId(2);
        catEirEmpty = new Catalog();
        catEirEmpty.setId(3);
        catEirFull = new Catalog();
        catEirFull.setId(4);
        catRandom = new Catalog();
        catRandom.setId(5);
        catOriginPreAssignment = new Catalog();
        catOriginPreAssignment.setId(6);

        when(catalogRepository.findIdByAlias("43080")).thenReturn(catEirGateIn.getId());
        when(catalogRepository.findIdByAlias("43081")).thenReturn(catEirGateOut.getId());
        when(catalogRepository.findIdByAlias("43083")).thenReturn(catEirEmpty.getId());
        when(catalogRepository.findIdByAlias("43084")).thenReturn(catEirFull.getId());
        when(catalogRepository.findIdByAlias("47747")).thenReturn(catOriginPreAssignment.getId());
    }

    void setupEstimateEmrData() {
        mockEstimateEmr = new EstimateEmr();
        mockEstimateEmr.setId(1);
        mockEstimateEmr.setEir(mockEir);
        mockEstimateEmr.setActive(true);
        mockEstimateEmr.setCatEstimateType(catRandom);
        mockEstimateEmr.setCatEstimateStatus(catRandom);
    }

    void setupChassisEstimateData() {
        EirChassis eirChassis = new EirChassis();
        eirChassis.setId(1);

        mockChassisEstimate = new ChassisEstimate();
        mockChassisEstimate.setId(1);
        mockChassisEstimate.setEirChassis(eirChassis);
        mockChassisEstimate.setActive(true);
        mockChassisEstimate.setCatChaestimStatus(catRandom);
    }

    void setupOkDeleteProcess() {
        Container container = new Container();
        container.setId(3);
        mockEir.setContainer(container);
        mockEir.setActive(true);

        mockCargoDocumentDetail = new CargoDocumentDetail();
        mockCargoDocumentDetail.setId(1);
        mockCargoDocumentDetail.setActive(true);
        mockEirDocumentCargoDetail = new EirDocumentCargoDetail();
        mockEirDocumentCargoDetail.setId(2);
        mockEirDocumentCargoDetail.setCargoDocumentDetail(mockCargoDocumentDetail);

        when(eirDocumentCargoDetailRepository.findByEirAndActiveTrue(mockEir.getId())).thenReturn(mockEirDocumentCargoDetail);
        when(cargoDocumentDetailRepository.findByIdAndActiveTrue(mockEirDocumentCargoDetail.getCargoDocumentDetail().getId())).thenReturn(mockCargoDocumentDetail);
    }

    @Test
    void Given_NullOrEmptyPpo_When_TruckDepartureDelete_Then_TransactionWillFailEmptyResponse() throws Exception {
        ResponseTruckDepartureDelete response;

        //NULL PPO
        response = truckDepartureDeleteService.truckDepartureDelete(null);
        assertEquals(0, response.getResponseState());
        assertEquals("", response.getResponseMessage());

        //EMPTY PPO
        response = truckDepartureDeleteService.truckDepartureDelete(new TruckDepartureDeleteInput.Input());
        assertEquals(0, response.getResponseState());
        assertEquals("", response.getResponseMessage());
    }

    @Test
    void Given_EirMovementNullOrEirNotActiveOrTruckDepartureDateNotNull_When_TruckDepartureDelete_Then_TransactionWillFail() throws Exception {
        ResponseTruckDepartureDelete response;

        setupMainParameters();
        setupCatalogs();

        //Eir Catalog Movement is NULL
        mockEir.setCatMovement(null);
        response = truckDepartureDeleteService.truckDepartureDelete(ppo);

        assertEquals(2, response.getResponseState());
        verify(messageLanguageRepository, times(1)).findMensaje(eq("GENERAL"), eq(15), anyInt());

        //Eir Active False
        mockEir.setActive(false);
        mockEir.setCatMovement(catRandom);
        response = truckDepartureDeleteService.truckDepartureDelete(ppo);

        assertEquals(2, response.getResponseState());
        verify(messageLanguageRepository, times(1)).findMensaje(eq("GENERAL"), eq(8), anyInt());

        //Truck Departure Date is NOT NULL
        mockEir.setActive(true);
        mockEir.setTruckDepartureDate(LocalDateTime.now());
        response = truckDepartureDeleteService.truckDepartureDelete(ppo);

        assertEquals(2, response.getResponseState());
        verify(messageLanguageRepository, times(1)).findMensaje(eq("TRUCK_DEPARTURE"), eq(1), anyInt());
    }

    @Test
    void Given_EirMovementGateInAndEmptyWithNotValidEstimateEmr_When_TruckDepartureDelete_Then_TransactionWillFail() throws Exception {
        ResponseTruckDepartureDelete response;

        setupMainParameters();
        setupCatalogs();
        setupEstimateEmrData();

        mockEir.setActive(true);
        mockEir.setCatMovement(catEirGateIn);
        mockEir.setCatEmptyFull(catEirEmpty);

        List<EstimateEmr> mockEstimateEmrList = new ArrayList<>();
        mockEstimateEmrList.add(mockEstimateEmr);

        when(sdgEstimateEmrRepository.findByEirAndCatEstimateStatusNotInListAndActiveTrue(eq(mockEir.getId()), anyList())).thenReturn(mockEstimateEmrList);
        when(messageLanguageRepository.findMensaje(eq("EIR"), eq(1), anyInt())).thenReturn("anyString");

        response = truckDepartureDeleteService.truckDepartureDelete(ppo);
        assertEquals(2, response.getResponseState());
        verify(messageLanguageRepository, times(1)).findMensaje(eq("EIR"), eq(1), anyInt());

    }

    @Test
    void Given_EirMovementGateInAndEmptyWithNotValidChassisEstimate_When_TruckDepartureDelete_Then_TransactionWillFail() throws Exception {
        ResponseTruckDepartureDelete response;

        setupMainParameters();
        setupCatalogs();
        setupChassisEstimateData();

        mockEir.setActive(true);
        mockEir.setCatMovement(catEirGateIn);
        mockEir.setCatEmptyFull(catEirEmpty);
        mockEir.setEirChassis(mockChassisEstimate.getEirChassis());

        List<ChassisEstimate> chassisEstimateList = new ArrayList<>();
        chassisEstimateList.add(mockChassisEstimate);

        when(chassisEstimateRepository.findByEirChassisIdAndNotInChaestimStatusList(eq(mockChassisEstimate.getEirChassis().getId()), anyList())).thenReturn(chassisEstimateList);
        when(messageLanguageRepository.findMensaje(eq("TRUCK_DEPARTURE"), eq(7), anyInt())).thenReturn("anyString");

        response = truckDepartureDeleteService.truckDepartureDelete(ppo);

        assertEquals(2, response.getResponseState());
        verify(messageLanguageRepository, times(1)).findMensaje(eq("TRUCK_DEPARTURE"), eq(7), anyInt());
    }

    @Test
    void Given_EirMovementGateOutAndEmptyAndContainerPreAssignmentFoundWithOption1_When_TruckDepartureDelete_Then_TransactionWillSucceed() throws Exception {
        ResponseTruckDepartureDelete response;

        setupMainParameters();
        setupCatalogs();
        setupOkDeleteProcess();

        mockEir.setCatMovement(catEirGateOut);
        mockEir.setCatEmptyFull(catEirEmpty);

        //ContainerPreAssignment Option 1
        ContainerPreassignment containerPreassignment = new ContainerPreassignment();
        containerPreassignment.setId(3);
        containerPreassignment.setActive(true);
        containerPreassignment.setCatOriginPreassignment(catRandom);
        containerPreassignment.setCargoDocumentDetail(mockCargoDocumentDetail);

        BookingDetail bookingDetail = new BookingDetail();
        bookingDetail.setId(1);
        bookingDetail.setActive(true);
        mockCargoDocumentDetail.setBookingDetail(bookingDetail);
        mockCargoDocumentDetail.setContainer(mockEir.getContainer());
        mockCargoDocumentDetail.setBookingDetail(bookingDetail);

        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        vesselProgrammingDetail.setId(1);
        mockEir.setVesselProgrammingDetail(vesselProgrammingDetail);

        when(eirRepository.findByContainerIdAndVesselProgrammingDetailIdAndActiveTrue(anyInt(), anyInt())).thenReturn(any(Eir.class));
        when(cargoDocumentDetailRepository.countFindByBookingDetailIdAndContainerIdNotNullAndActiveTrueAndBookingDetailActiveTrueAndActiveTrue(mockCargoDocumentDetail.getBookingDetail().getId())).thenReturn(2);
        when(bookingDetailRepository.findByIdAndActiveTrue(mockCargoDocumentDetail.getBookingDetail().getId())).thenReturn(bookingDetail);
        when(containerPreassignmentRepository.findByCargoDocumentDetailIdAndActiveTrue(mockCargoDocumentDetail.getId())).thenReturn(containerPreassignment);

        //ActivityLog
        when(activityLogRepository.findByEirNumber(mockEir.getId())).thenReturn(null);
        //Disable sdy movement Instruction
        when(businessUnitRepository.findById(mockSubBusinessUnitLocal.getId())).thenReturn(Optional.ofNullable(mockSubBusinessUnitLocal));
        when(systemRuleRepository.findByAliasAndActiveTrue("sd1_rule_integration_sdy")).thenReturn(null);
        when(checkYardIntegrationService.checkYardIntegration(mockSubBusinessUnitLocal.getBusinesUnitAlias(), "gateout")).thenReturn(false);

        response = truckDepartureDeleteService.truckDepartureDelete(ppo);

        //Asserts:
        verify(cargoDocumentDetailRepository, times(1)).save(mockCargoDocumentDetail);
        verify(containerPreassignmentRepository, times(1)).save(containerPreassignment);
        verify(bookingDetailRepository, times(1)).save(bookingDetail);
        verify(eirRepository, times(1)).save(mockEir);
        verify(eirDocumentCargoDetailRepository, times(1)).save(mockEirDocumentCargoDetail);
        verify(messageLanguageRepository, times(1)).findMensaje(eq("GENERAL"), eq(7), anyInt());
        assertEquals(1, response.getResponseState());
    }

    @Test
    void Given_EirMovementGateOutAndEmptyAndContainerPreAssignmentFoundWithOption2_When_TruckDepartureDelete_Then_TransactionWillSucceed() throws Exception {
        ResponseTruckDepartureDelete response;

        setupMainParameters();
        setupCatalogs();
        setupOkDeleteProcess();

        mockEir.setCatMovement(catEirGateOut);
        mockEir.setCatEmptyFull(catEirEmpty);

        //ContainerPreAssignment Option 2
        BookingDetail bookingDetail = new BookingDetail();
        bookingDetail.setId(1);
        bookingDetail.setActive(true);
        mockCargoDocumentDetail.setBookingDetail(bookingDetail);
        mockCargoDocumentDetail.setContainer(mockEir.getContainer());
        mockCargoDocumentDetail.setBookingDetail(bookingDetail);

        ContainerPreassignment containerPreAssignment2 = new ContainerPreassignment();
        containerPreAssignment2.setId(4);
        containerPreAssignment2.setActive(true);
        containerPreAssignment2.setBookingDetail(bookingDetail);
        containerPreAssignment2.setCatOriginPreassignment(catRandom);
        containerPreAssignment2.setCargoDocumentDetail(mockCargoDocumentDetail);

        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        vesselProgrammingDetail.setId(1);
        mockEir.setVesselProgrammingDetail(vesselProgrammingDetail);
        when(eirRepository.findByContainerIdAndVesselProgrammingDetailIdAndActiveTrue(anyInt(), anyInt())).thenReturn(any(Eir.class));
        when(cargoDocumentDetailRepository.countFindByBookingDetailIdAndContainerIdNotNullAndActiveTrueAndBookingDetailActiveTrueAndActiveTrue(mockCargoDocumentDetail.getBookingDetail().getId())).thenReturn(2);
        when(bookingDetailRepository.findByIdAndActiveTrue(mockCargoDocumentDetail.getBookingDetail().getId())).thenReturn(bookingDetail);

        when(containerPreassignmentRepository.findByCargoDocumentDetailIdAndActiveTrue(mockCargoDocumentDetail.getId())).thenReturn(null);
        when(containerPreassignmentRepository.findByBookingDetailIdAndContainerIdAndActiveTrueOrderByRegistrationDateDesc(anyInt(), anyInt())).thenReturn(containerPreAssignment2);

        //ActivityLog
        when(activityLogRepository.findByEirNumber(mockEir.getId())).thenReturn(null);
        //Disable sdy movement Instruction
        when(businessUnitRepository.findById(mockSubBusinessUnitLocal.getId())).thenReturn(Optional.ofNullable(mockSubBusinessUnitLocal));
        when(systemRuleRepository.findByAliasAndActiveTrue("sd1_rule_integration_sdy")).thenReturn(null);
        when(checkYardIntegrationService.checkYardIntegration(mockSubBusinessUnitLocal.getBusinesUnitAlias(), "gateout")).thenReturn(false);

        response = truckDepartureDeleteService.truckDepartureDelete(ppo);

        //Asserts:
        verify(cargoDocumentDetailRepository, times(1)).save(mockCargoDocumentDetail);
        verify(containerPreassignmentRepository, times(1)).save(containerPreAssignment2);
        verify(bookingDetailRepository, times(1)).save(bookingDetail);
        verify(eirRepository, times(1)).save(mockEir);
        verify(eirDocumentCargoDetailRepository, times(1)).save(mockEirDocumentCargoDetail);
        verify(messageLanguageRepository, times(1)).findMensaje(eq("GENERAL"), eq(7), anyInt());
        assertEquals(1, response.getResponseState());
    }

    @Test
    void Given_EirMovementGateInOrGateOutAndFull_When_TruckDepartureDelete_Then_TransactionWillSucceed() throws Exception {
        ResponseTruckDepartureDelete response;

        setupMainParameters();
        setupCatalogs();
        setupOkDeleteProcess();

        mockEir.setCatMovement(catEirGateOut);
        mockEir.setCatEmptyFull(catEirFull);

        VesselProgrammingContainer vesselProgrammingContainer = new VesselProgrammingContainer();
        vesselProgrammingContainer.setId(1);
        vesselProgrammingContainer.setActive(true);
        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        vesselProgrammingDetail.setId(2);
        vesselProgrammingDetail.setActive(true);
        TransportPlanningDetail transportPlanningDetail = new TransportPlanningDetail();
        transportPlanningDetail.setId(2);
        mockEir.setVesselProgrammingDetail(vesselProgrammingDetail);
        mockEir.setTransportPlanningDetailFull(transportPlanningDetail);

        when(eirDocumentCargoDetailRepository.findOneByEirId(mockEir.getId())).thenReturn(mockEirDocumentCargoDetail);
        when(vesselProgrammingContainerRepository.findByContainer_IdAndVesselProgrammingDetail_IdAndActiveTrue(mockEir.getContainer().getId(), mockEir.getVesselProgrammingDetail().getId())).thenReturn(vesselProgrammingContainer);
        when(transportPlanningDetailRepository.findOneById(mockEir.getTransportPlanningDetailFull().getId())).thenReturn(transportPlanningDetail);

        response = truckDepartureDeleteService.truckDepartureDelete(ppo);

        //Asserts:
        verify(cargoDocumentDetailRepository, times(1)).save(mockCargoDocumentDetail);
        verify(vesselProgrammingContainerRepository, times(1)).save(vesselProgrammingContainer);//?
        verify(vesselProgrammingContainerRepository, times(1)).save(vesselProgrammingContainer);//?
        verify(transportPlanningDetailRepository, times(1)).save(transportPlanningDetail);//?
        verify(eirRepository, times(1)).save(mockEir);
        verify(eirDocumentCargoDetailRepository, times(1)).save(mockEirDocumentCargoDetail);
        assertEquals(1, response.getResponseState());
        verify(messageLanguageRepository, times(1)).findMensaje(eq("GENERAL"), eq(7), anyInt());
    }

    @Test
    void Given_EirMovementGateInAndEmptyWithValidEstimateEmr_When_TruckDepartureDelete_Then_TransactionWillSucceed() throws Exception {
        ResponseTruckDepartureDelete response;

        setupMainParameters();
        setupCatalogs();
        setupOkDeleteProcess();
        setupEstimateEmrData();

        mockEir.setCatMovement(catEirGateIn);
        mockEir.setCatEmptyFull(catEirEmpty);

        EstimateEmrDetail estimateEmrDetail11 = new EstimateEmrDetail();
        EstimateEmrDetail estimateEmrDetail12 = new EstimateEmrDetail();
        estimateEmrDetail11.setId(1);
        estimateEmrDetail11.setActive(true);
        estimateEmrDetail11.setEstimateEmr(mockEstimateEmr);
        estimateEmrDetail12.setId(2);
        estimateEmrDetail12.setActive(true);
        estimateEmrDetail12.setEstimateEmr(mockEstimateEmr);

        List<EstimateEmr> estimateList = new ArrayList<>();
        estimateList.add(mockEstimateEmr);
        List<EstimateEmrDetail> estimateEmrDetailList = new ArrayList<>();
        estimateEmrDetailList.add(estimateEmrDetail11);
        estimateEmrDetailList.add(estimateEmrDetail12);

        when(sdgEstimateEmrRepository.findByEirAndCatEstimateStatusNotInListAndActiveTrue(eq(mockEir.getId()), anyList())).thenReturn(new ArrayList<>());
        when(sdgEstimateEmrRepository.findByEirAndCatEstimateStatusInListAndActiveTrue(eq(mockEir.getId()), anyList())).thenReturn(estimateList);
        when(estimateEmrDetailRepository.findByEstimateEmr_IdAndActiveTrue(mockEstimateEmr.getId())).thenReturn(estimateEmrDetailList);

        response = truckDepartureDeleteService.truckDepartureDelete(ppo);

        verify(estimateEmrDetailRepository, times(2)).save(any());//?
        verify(sdgEstimateEmrRepository, times(1)).save(any());//?
        verify(eirRepository, times(1)).save(mockEir);
        verify(eirDocumentCargoDetailRepository, times(1)).save(mockEirDocumentCargoDetail);
        assertEquals(1, response.getResponseState());
        verify(messageLanguageRepository, times(1)).findMensaje(eq("GENERAL"), eq(7), anyInt());
    }

    @Test
    void Given_EirMovementGateInAndEmptyWithEirChassis_When_TruckDepartureDelete_Then_TransactionWillSucceed() throws Exception {
        ResponseTruckDepartureDelete response;

        setupMainParameters();
        setupCatalogs();
        setupOkDeleteProcess();

        mockEir.setCatMovement(catEirGateIn);
        mockEir.setCatEmptyFull(catEirEmpty);

        EirChassis eirChassis = new EirChassis();
        eirChassis.setId(1);
        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();
        chassisDocumentDetail.setId(1);
        chassisDocumentDetail.setActive(true);
        eirChassis.setChassisDocumentDetail(chassisDocumentDetail);

        mockEir.setEirChassis(eirChassis);
        mockEir.setFlagChassisStayed(true);

        when(cargoDocumentDetailRepository.findByIdAndActiveTrue(mockEirDocumentCargoDetail.getId())).thenReturn(mockCargoDocumentDetail);
        when(chassisEstimateRepository.findByEirChassisIdAndNotInChaestimStatusList(eq(eirChassis.getId()), anyList())).thenReturn(new ArrayList<>());
        when(chassisEstimateRepository.findByEirChassisIdAndInChaestimStatusList(eq(eirChassis.getId()), anyList())).thenReturn(new ArrayList<>());

        response = truckDepartureDeleteService.truckDepartureDelete(ppo);

        verify(chassisDocumentDetailRepository, times(1)).save(mockEir.getEirChassis().getChassisDocumentDetail());
        verify(eirChassisRepository, times(1)).save(mockEir.getEirChassis());
        verify(eirRepository, times(1)).save(mockEir);
        verify(eirDocumentCargoDetailRepository, times(1)).save(mockEirDocumentCargoDetail);
        assertEquals(1, response.getResponseState());
        verify(messageLanguageRepository, times(1)).findMensaje(eq("GENERAL"), eq(7), anyInt());
    }

    @Test
    void Given_EirMovementGateInAndEmptyWithEirChassisWithValidChassisEstimate_When_TruckDepartureDelete_Then_TransactionWillSucceed() throws Exception {
        ResponseTruckDepartureDelete response;

        setupMainParameters();
        setupCatalogs();
        setupOkDeleteProcess();
        setupChassisEstimateData();

        mockEir.setCatMovement(catEirGateIn);
        mockEir.setCatEmptyFull(catEirEmpty);

        ChassisEstimateDetail estimateEmrDetail11 = new ChassisEstimateDetail();
        ChassisEstimateDetail estimateEmrDetail12 = new ChassisEstimateDetail();
        estimateEmrDetail11.setId(1);
        estimateEmrDetail11.setActive(true);
        estimateEmrDetail11.setChassisEstimate(mockChassisEstimate);
        estimateEmrDetail12.setId(2);
        estimateEmrDetail12.setActive(true);
        estimateEmrDetail12.setChassisEstimate(mockChassisEstimate);

        List<ChassisEstimate> estimateList = new ArrayList<>();
        estimateList.add(mockChassisEstimate);
        List<ChassisEstimateDetail> chassisEstimateDetailList1 = new ArrayList<>();
        chassisEstimateDetailList1.add(estimateEmrDetail11);
        chassisEstimateDetailList1.add(estimateEmrDetail12);

        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();
        chassisDocumentDetail.setId(1);
        chassisDocumentDetail.setActive(true);

        mockEir.setEirChassis(mockChassisEstimate.getEirChassis());
        mockEir.setFlagChassisStayed(true);
        mockEir.getEirChassis().setChassisDocumentDetail(chassisDocumentDetail);

        when(cargoDocumentDetailRepository.findByIdAndActiveTrue(mockEirDocumentCargoDetail.getId())).thenReturn(mockCargoDocumentDetail);
        when(chassisEstimateRepository.findByEirChassisIdAndNotInChaestimStatusList(eq(mockChassisEstimate.getEirChassis().getId()), anyList())).thenReturn(new ArrayList<>());
        when(chassisEstimateRepository.findByEirChassisIdAndInChaestimStatusList(eq(mockChassisEstimate.getEirChassis().getId()), anyList())).thenReturn(estimateList);
        when(chassisEstimateDetailRepository.findByChassisEstimateIdAndActiveTrue(mockChassisEstimate.getId())).thenReturn(chassisEstimateDetailList1);

        response = truckDepartureDeleteService.truckDepartureDelete(ppo);

        //Asserts
        verify(chassisEstimateDetailRepository, times(2)).save(any());
        verify(chassisEstimateRepository, times(1)).save(any());
        verify(chassisDocumentDetailRepository, times(1)).save(mockEir.getEirChassis().getChassisDocumentDetail());
        verify(eirChassisRepository, times(1)).save(mockEir.getEirChassis());
        verify(eirRepository, times(1)).save(mockEir);
        verify(eirDocumentCargoDetailRepository, times(1)).save(mockEirDocumentCargoDetail);
        assertEquals(1, response.getResponseState());
        verify(messageLanguageRepository, times(1)).findMensaje(eq("GENERAL"), eq(7), anyInt());
    }

    @Test
    void Given_EirMovementGateInAndEmptyWithEirChassisWithFlagChassisStayedFalse_When_TruckDepartureDelete_Then_TransactionWillSucceed() throws Exception {
        ResponseTruckDepartureDelete response;

        setupMainParameters();
        setupCatalogs();
        setupOkDeleteProcess();

        mockEir.setCatMovement(catEirGateIn);
        mockEir.setCatEmptyFull(catEirEmpty);

        EirChassis eirChassis = new EirChassis();
        eirChassis.setId(1);
        eirChassis.setActive(true);
        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();
        chassisDocumentDetail.setId(1);
        chassisDocumentDetail.setActive(true);
        Chassis chassisAux = new Chassis();
        chassisAux.setId(1);
        chassisAux.setActive(true);
        eirChassis.setChassis(chassisAux);
        eirChassis.setChassisDocumentDetail(chassisDocumentDetail);

        mockEir.setEirChassis(eirChassis);
        mockEir.setFlagChassisStayed(false);

        Eir eirAux = new Eir();
        eirAux.setId(9);
        eirAux.setActive(true);
        eirAux.setCatEmptyFull(catEirEmpty);
        eirAux.setCatMovement(catEirGateOut);
        eirAux.setLocalSubBusinessUnit(mockSubBusinessUnitLocal);

        Container containerNotApplied = new Container();
        containerNotApplied.setId(9);
        containerNotApplied.setActive(true);
        containerNotApplied.setContainerNumber("NOT APPLICA");

        eirAux.setContainer(containerNotApplied);

        EirChassis eirChassisAux = new EirChassis();
        eirChassisAux.setId(9);
        eirChassisAux.setActive(true);
        ChassisDocumentDetail chassisDocumentDetailAux = new ChassisDocumentDetail();
        chassisDocumentDetailAux.setId(9);
        chassisDocumentDetailAux.setActive(true);
        ChassisBookingDocument chassisBookingDocumentAux = new ChassisBookingDocument();
        chassisBookingDocumentAux.setId(10);
        chassisBookingDocumentAux.setActive(true);

        eirChassisAux.setChassis(chassisAux);
        eirAux.setEirChassis(eirChassisAux);
        chassisDocumentDetailAux.setChassisBookingDocument(chassisBookingDocumentAux);
        eirChassisAux.setChassisDocumentDetail(chassisDocumentDetailAux);

        Truck truckEir = new Truck();
        truckEir.setId(9);
        Person driverPerson = new Person();
        driverPerson.setId(9);

        mockEir.setTruck(truckEir);
        mockEir.setDriverPerson(driverPerson);
        mockEir.setTruckArrivalDate(LocalDateTime.now());

        eirAux.setTruck(truckEir);
        eirAux.setDriverPerson(driverPerson);
        eirAux.setTruckArrivalDate(LocalDateTime.now());

        when(eirRepository.findByContainerIdAndLocalSubBusinessUnitIdAndCatMovementIdAndTruckIdAndDriverPersonIdAndTruckArrivalDateBetweenAndActiveTrue(
                anyInt(), eq(mockSubBusinessUnitLocal.getId()), eq(catEirGateOut.getId()), eq(truckEir.getId()), eq(driverPerson.getId()), eq(mockEir.getTruckArrivalDate().minusMinutes(5)), eq(mockEir.getTruckArrivalDate().plusMinutes(5)))).thenReturn(eirAux);
        when(cargoDocumentDetailRepository.findByIdAndActiveTrue(mockEirDocumentCargoDetail.getId())).thenReturn(mockCargoDocumentDetail);
        when(chassisEstimateRepository.findByEirChassisIdAndNotInChaestimStatusList(eq(eirChassis.getId()), anyList())).thenReturn(new ArrayList<>());
        when(chassisEstimateRepository.findByEirChassisIdAndInChaestimStatusList(eq(eirChassis.getId()), anyList())).thenReturn(new ArrayList<>());

        response = truckDepartureDeleteService.truckDepartureDelete(ppo);

        //Asserts
        verify(chassisDocumentDetailRepository, times(1)).save(mockEir.getEirChassis().getChassisDocumentDetail());
        verify(eirChassisRepository, times(1)).save(mockEir.getEirChassis());
        verify(eirRepository, times(1)).save(mockEir);
        verify(eirDocumentCargoDetailRepository, times(1)).save(mockEirDocumentCargoDetail);
        verify(chassisDocumentDetailRepository, times(1)).save(eirChassisAux.getChassisDocumentDetail());
        verify(chassisBookingDocumentRepository, times(1)).save(eirAux.getEirChassis().getChassisDocumentDetail().getChassisBookingDocument());
        verify(eirRepository, times(1)).save(eirAux);
        verify(eirChassisRepository, times(1)).save(eirChassisAux);
        assertEquals(1, response.getResponseState());
        verify(messageLanguageRepository, times(1)).findMensaje(eq("GENERAL"), eq(7), anyInt());
    }

    @Test
    void Given_EirMovementGateOutAndEmptyWithEirChassisWithFlagChassisPickupFalse_When_TruckDepartureDelete_Then_TransactionWillSucceed() throws Exception {
        ResponseTruckDepartureDelete response;

        setupMainParameters();
        setupCatalogs();
        setupOkDeleteProcess();

        mockEir.setCatMovement(catEirGateOut);
        mockEir.setCatEmptyFull(catEirEmpty);

        //ContainerPreAssignment Option1
        ContainerPreassignment containerPreassignment = new ContainerPreassignment();
        containerPreassignment.setId(3);
        containerPreassignment.setActive(true);
        containerPreassignment.setCatOriginPreassignment(catRandom);
        containerPreassignment.setCargoDocumentDetail(mockCargoDocumentDetail);

        BookingDetail bookingDetail = new BookingDetail();
        bookingDetail.setId(1);
        bookingDetail.setActive(true);
        mockCargoDocumentDetail.setBookingDetail(bookingDetail);
        mockCargoDocumentDetail.setContainer(mockEir.getContainer());
        mockCargoDocumentDetail.setBookingDetail(bookingDetail);

        VesselProgrammingDetail vesselProgrammingDetail = new VesselProgrammingDetail();
        vesselProgrammingDetail.setId(1);
        mockEir.setVesselProgrammingDetail(vesselProgrammingDetail);

        EirChassis eirChassis = new EirChassis();
        eirChassis.setId(1);
        eirChassis.setActive(true);
        ChassisDocumentDetail chassisDocumentDetail = new ChassisDocumentDetail();
        chassisDocumentDetail.setId(1);
        chassisDocumentDetail.setActive(true);
        ChassisBookingDocument chassisBookingDocument = new ChassisBookingDocument();
        chassisBookingDocument.setId(1);
        chassisBookingDocument.setActive(true);
        Chassis chassisAux = new Chassis();
        chassisAux.setId(1);
        chassisAux.setActive(true);

        eirChassis.setChassis(chassisAux);
        chassisDocumentDetail.setChassisBookingDocument(chassisBookingDocument);
        eirChassis.setChassisDocumentDetail(chassisDocumentDetail);

        mockEir.setEirChassis(eirChassis);
        mockEir.setFlagChassisPickup(false);

        Eir eirAux = new Eir();
        eirAux.setId(9);
        eirAux.setActive(true);
        eirAux.setCatEmptyFull(catEirEmpty);
        eirAux.setCatMovement(catEirGateIn);
        eirAux.setLocalSubBusinessUnit(mockSubBusinessUnitLocal);

        Container containerNotApplica = new Container();
        containerNotApplica.setId(9);
        containerNotApplica.setActive(true);
        containerNotApplica.setContainerNumber("NOT APPLICA");

        eirAux.setContainer(containerNotApplica);

        EirChassis eirChassisAux = new EirChassis();
        eirChassisAux.setId(9);
        eirChassisAux.setActive(true);
        ChassisDocumentDetail chassisDocumentDetailAux = new ChassisDocumentDetail();
        chassisDocumentDetailAux.setId(9);
        chassisDocumentDetailAux.setActive(true);
        ChassisBookingDocument chassisBookingDocumentAux = new ChassisBookingDocument();
        chassisBookingDocumentAux.setId(10);
        chassisBookingDocumentAux.setActive(true);

        eirChassisAux.setChassis(chassisAux);
        eirAux.setEirChassis(eirChassisAux);
        chassisDocumentDetailAux.setChassisBookingDocument(chassisBookingDocumentAux);
        eirChassisAux.setChassisDocumentDetail(chassisDocumentDetailAux);

        Truck truckEir = new Truck();
        truckEir.setId(9);
        Person driverPerson = new Person();
        driverPerson.setId(9);

        mockEir.setTruck(truckEir);
        mockEir.setDriverPerson(driverPerson);
        mockEir.setTruckArrivalDate(LocalDateTime.now());

        eirAux.setTruck(truckEir);
        eirAux.setDriverPerson(driverPerson);
        eirAux.setTruckArrivalDate(LocalDateTime.now());

        when(eirRepository.findByContainerIdAndLocalSubBusinessUnitIdAndCatMovementIdAndTruckIdAndDriverPersonIdAndTruckArrivalDateBetweenAndActiveTrue(
                anyInt(), eq(mockSubBusinessUnitLocal.getId()), eq(catEirGateIn.getId()), eq(truckEir.getId()), eq(driverPerson.getId()), eq(mockEir.getTruckArrivalDate().minusMinutes(5)), eq(mockEir.getTruckArrivalDate().plusMinutes(5)))).thenReturn(eirAux);

        when(chassisEstimateRepository.findByEirChassisIdAndNotInChaestimStatusList(eq(eirChassis.getId()), anyList())).thenReturn(new ArrayList<>());
        when(chassisEstimateRepository.findByEirChassisIdAndInChaestimStatusList(eq(eirChassis.getId()), anyList())).thenReturn(new ArrayList<>());
        when(chassisDocumentDetailRepository.countFindByChassisBookingDocumentIdAndChassisIdNotNullAndActiveTrue(anyInt())).thenReturn(2);
        when(containerPreassignmentRepository.findByCargoDocumentDetailIdAndActiveTrue(mockCargoDocumentDetail.getId())).thenReturn(containerPreassignment);
        when(eirRepository.findByContainerIdAndVesselProgrammingDetailIdAndActiveTrue(anyInt(), anyInt())).thenReturn(any(Eir.class));
        when(cargoDocumentDetailRepository.countFindByBookingDetailIdAndContainerIdNotNullAndActiveTrueAndBookingDetailActiveTrueAndActiveTrue(mockCargoDocumentDetail.getBookingDetail().getId())).thenReturn(2);
        when(bookingDetailRepository.findByIdAndActiveTrue(mockCargoDocumentDetail.getBookingDetail().getId())).thenReturn(bookingDetail);

        response = truckDepartureDeleteService.truckDepartureDelete(ppo);

        //Asserts
        verify(chassisDocumentDetailRepository, times(1)).save(mockEir.getEirChassis().getChassisDocumentDetail());
        verify(chassisBookingDocumentRepository, times(1)).save(mockEir.getEirChassis().getChassisDocumentDetail().getChassisBookingDocument());
        verify(eirChassisRepository, times(1)).save(mockEir.getEirChassis());
        verify(eirRepository, times(1)).save(mockEir);
        verify(eirDocumentCargoDetailRepository, times(1)).save(mockEirDocumentCargoDetail);
        verify(chassisDocumentDetailRepository, times(1)).save(eirChassisAux.getChassisDocumentDetail());
        verify(eirRepository, times(1)).save(eirAux);
        verify(eirChassisRepository, times(1)).save(eirChassisAux);
        assertEquals(1, response.getResponseState());
        verify(messageLanguageRepository, times(1)).findMensaje(eq("GENERAL"), eq(7), anyInt());
    }
}
