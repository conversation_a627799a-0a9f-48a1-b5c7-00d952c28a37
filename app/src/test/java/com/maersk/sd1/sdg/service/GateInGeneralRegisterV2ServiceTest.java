package com.maersk.sd1.sdg.service;


import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.maersk.sd1.common.integration.SD1IntegrationConnection;
import com.maersk.sd1.common.integration.SD1IntegrationConnectionFactory;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.common.service.MessageLanguageService;
import com.maersk.sd1.ges.service.GESCatalogService;
import com.maersk.sd1.sdg.controller.dto.GateInGeneralRegisterV2Input;
import com.maersk.sd1.sdg.controller.dto.GateInGeneralRegisterV2Output;
import com.maersk.sd1.sdg.controller.dto.GateOutGeneralRegisterOutput;
import com.maersk.sd1.sdg.dto.GateInGeneralRegisterV2Chassis;
import com.maersk.sd1.sdg.dto.GateInGeneralRegisterV2Equipment;
import com.maersk.sd1.sdg.repository.GateInGeneralRegisterV2Repository;
import org.json.JSONObject;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

import static com.maersk.sd1.common.Parameter.*;
import static org.junit.Assert.assertNull;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.Mockito.*;


class GateInGeneralRegisterV2ServiceTest {

    @Mock
    private GESCatalogService catalogService;

    @Mock
    private BusinessUnitRepository businessUnitRepository;

    @Mock
    private MessageLanguageService messageLanguageService;

    @Mock
    private ContainerRepository containerRepository;

    @Mock
    private IsoCodeRepository isoCodeRepository;

    @Mock
    private GateInGeneralRegisterV2Repository gateInGeneralRegisterV2Repository;

    @Mock
    private SystemRuleRepository systemRuleRepository;

    @Mock
    private EirRepository eirRepository;

    @Mock
    private EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;

    @Mock
    private EirZoneRepository eirZoneRepository;

    @Mock
    private EmrInspectionRepository emrInspectionRepository;

    @Mock
    private VesselProgrammingContainerRepository vesselProgrammingContainerRepository;

    @Mock
    private TransportPlanningDetailRepository transportPlanningDetailRepository;

    @Mock
    private AttachmentRepository attachmentRepository;

    @Mock
    private VesselProgrammingContainerImoRepository vesselProgrammingContainerImoRepository;

    @Mock
    private SdfEirPhotoRepository sdfEirPhotoRepository;

    @Mock
    private SdsEirPhotoRepository sdsEirPhotoRepository;

    @Mock
    private EirMultipleRepository eirMultipleRepository;

    @Mock
    private ChassisRepository chassisRepository;

    @Mock
    private ShippingLineRepository shippingLineRepository;

    @Mock
    private VesselRepository vesselRepository;

    @Mock
    private VesselProgrammingRepository vesselProgrammingRepository;

    @Mock
    private VesselProgrammingDetailRepository vesselProgrammingDetailRepository;

    @Mock
    private EirChassisZoneRepository eirChassisZoneRepository;

    @Mock
    private EirChassisRepository eirChassisRepository;

    @Mock
    private ChassisDocumentDetailRepository chassisDocumentDetailRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private EirChassisZoneActivityRepository eirChassisZoneActivityRepository;

    @Mock
    private ChassisDocumentRepository chassisDocumentRepository;

    @Mock
    private DischargeRepository dischargeRepository;

    @Mock
    private StockChassisRepository stockChassisRepository;

    @Mock
    private CargoDocumentDetailRepository cargoDocumentDetailRepository;

    @Mock
    private Environment env;

    @Mock
    private SD1IntegrationConnectionFactory connectionFactory;

    @Mock
    private SD1IntegrationConnection connection;

    @Mock
    private GateOutGeneralRegisterService gateOutGeneralRegisterService;

    @InjectMocks
    private GateInGeneralRegisterV2Service gateInGeneralRegisterV2Service;

    public HashMap<String, Integer> mockCatalogs() {
        List<String> catalogAlias = Arrays.asList(
                CATALOG_TYPE_GATE_IS_GATEIN_ALIAS,
                CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS,
                CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS,
                CATALOG_TYPE_PROCESS_IS_FULL_ALIAS,
                CATALOG_MEASURE_WEIGHT_KG_ALIAS,
                CATALOG_MEANS_TRANSPORT,
                CATALOG_NO_GRADE,
                CATALOG_PENDING_INSPECTION,
                CATALOG_DEPOT_OPER_MTY_DISCHARGE,
                CATALOG_GI_TYPE_MTY_DISCHARGE,
                CATALOG_GI_GENERAL_CREATION,
                CATALOG_CUSTOMER_RETURN,
                CATALOG_CHASSIS_IN_PROGRESS,
                CATALOG_TRK_PLAN_IN_PROCESS,
                CATALOG_CONTAINER_CREATION_SOURCE_LOAD_MASTER,
                CATALOG_CONTAINER_CREATION_SOURCE_FLEET_EDI,
                CATALOG_CONTAINER_UPDATE_SOURCE_FLEET_EDI
                );
        Integer pvt = 0;
        HashMap<String, Integer> catalogIds = new HashMap<>();
        for (String ca : catalogAlias) {
            catalogIds.put(ca, ++pvt);
        }
        return catalogIds;
    }

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void givenEmptyContainerRequest_whenRegisterGateIn_returnCreatedEir() throws Exception {
        //GIVEN
        HashMap<String, Integer> catalogIds = mockCatalogs();
        Integer subBusinessUnitLocalId = 3, driverId = 1, vehicleId = 1, truckCompanyId = 1, userRegistrationId = 1, languageId = 1,
                containerId = 1, isoCodeId = 1, cargoDocumentDetailId = 1, vesselProgrammingDetailId = 1, consigneeCompanyId = 2,
                shippingLineId = 1, systemRuleId = 1, vesselProgrammingContainerId = 1, eirId = 1, eirDocumentCargoDetailId = 1;
        String containerNumber = "TEHC4000000", subBusinessUnitLocalAlias = "ALIAS";
        Integer catEngineBrandId = 100, catReeferTypeId = 101, catCargoDocumentTypeId = 103, catContainerSizeId = catalogIds.get(CATALOG_SIZE_CONTAINER_40_ALIAS),
                catContainerTypeId = catalogIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS), catMeasureWeight = 105, catEmptyContainerId = catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS);
        Integer subBusinessUnitId = 2, businessUnitId = 1;
        Pageable pageable = PageRequest.of(0, 1);

        GateInGeneralRegisterV2Input.Input input = GateInGeneralRegisterV2Input.Input.builder()
                .subBusinessUnitLocalId(subBusinessUnitLocalId)
                .equipmentCatEmptyFullId(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS))
                .equipment("[{\"programacion_nave_detalle_id\":1,\"documento_carga_detalle_id\":1,\"planning_detail_id\":null,\"equipment_id\":1,\"equipment_number\":\"TEHC4000000\",\"iso_code_id\":1,\"manifest_weight\":null,\"manifest_weight_unit_id\":null,\"temperature\":\"\",\"temperature_unit_id\":null,\"seal_1\":\"\",\"seal_2\":\"\",\"seal_3\":\"\",\"seal_4\":\"\",\"reefer_connect\":null,\"reefer_dangerous\":null,\"imos\":[],\"description_dangerous_substance\":\"\",\"pictures\":[{\"id\":\"262a3171-3692-4af7-a461-3376ae25a286\",\"nombre\":\"foto_1\",\"formato\":\"jpg\",\"peso\":97263,\"ubicacion\":\"Gate/Gate_In/\",\"tipo_adjunto\":48028,\"accion\":\"N\",\"url\":\"https://maerskapmtisstgsd1prep.blob.core.windows.net/sdg/Gate%2FGate_In%2F262a3171-3692-4af7-a461-3376ae25a286?sv=2021-06-08&ss=bfqt&srt=sco&sp=rwdlacupiytfx&se=2029-01-06T22:39:50Z&st=2023-01-06T14:39:50Z&spr=https&sig=702KS74M87UyeusjEaLNMAMe15IrglRqDpA7x%2BJwAZ4%3D\"},{\"id\":\"5b21ef65-5621-4736-9287-c1c2ae7e33f3\",\"nombre\":\"foto_2\",\"formato\":\"jpg\",\"peso\":97263,\"ubicacion\":\"Gate/Gate_In/\",\"tipo_adjunto\":48028,\"accion\":\"N\",\"url\":\"https://maerskapmtisstgsd1prep.blob.core.windows.net/sdg/Gate%2FGate_In%2F5b21ef65-5621-4736-9287-c1c2ae7e33f3?sv=2021-06-08&ss=bfqt&srt=sco&sp=rwdlacupiytfx&se=2029-01-06T22:39:50Z&st=2023-01-06T14:39:50Z&spr=https&sig=702KS74M87UyeusjEaLNMAMe15IrglRqDpA7x%2BJwAZ4%3D\"}],\"aps_id\":\"0\",\"no_maersk\":1}]")
                .moveTypeId(catalogIds.get(CATALOG_CUSTOMER_RETURN))
                .chassis("{}")
                .driverId(driverId)
                .vehicleId(vehicleId)
                .truckCompanyId(truckCompanyId)
                .comments("")
                .userRegistrationId(userRegistrationId)
                .languageId(languageId)
                .operationCode("DEVCLNTE")
                .inputChassisNumber("")
                .flagChassisStayed(false)
                .inspectionSitu(true)
                .twrNumber("")
                .systemRuleId("sd1_rule_integration_sdy")
                .build();

        GateInGeneralRegisterV2Input.Root request = GateInGeneralRegisterV2Input.Root.builder()
                .prefix(GateInGeneralRegisterV2Input.Prefix.builder()
                        .input(input)
                        .build())
                .build();

        Gson gson = new Gson();
        Type equipmentListType = new TypeToken<ArrayList<GateInGeneralRegisterV2Equipment>>() {
        }.getType();

        List<GateInGeneralRegisterV2Equipment> equipmentInput = gson.fromJson(input.getEquipment(), equipmentListType);


        Container container = Container.builder()
                .id(containerId)
                .containerNumber(containerNumber)
                .containerTare(0)
                .catEquipMeasureTare(Catalog.builder().id(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).build())
                .maximunPayload(1)
                .catEquipMeasurePayload(Catalog.builder().id(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).build())
                .catGrade(null)
                .manufactureDate(LocalDateTime.now())
                .catEngineBrand(Catalog.builder().id(catEngineBrandId).build())
                .catReeferType(Catalog.builder().id(catReeferTypeId).build())
                .containerNumber(input.getEquipment())
                .isoCode(IsoCode.builder().id(isoCodeId).build())
                .catSize(Catalog.builder().id(catalogIds.get(CATALOG_SIZE_CONTAINER_40_ALIAS)).build())
                .catContainerType(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS)).build())
                .build();

        CargoDocumentDetail cargoDocumentDetail = (CargoDocumentDetail.builder()
                .cargoDocument(CargoDocument.builder()
                        .id(cargoDocumentDetailId)
                        .vesselProgrammingDetail(VesselProgrammingDetail.builder()
                                .id(vesselProgrammingDetailId)
                                .build())
                        .consigneeCompany(Company.builder()
                                .id(consigneeCompanyId)
                                .build())
                        .shippingLine(ShippingLine.builder()
                                .id(shippingLineId)
                                .build())
                        .cargoDocument("CD")
                        .catCargoDocumentType(Catalog.builder()
                                .id(catCargoDocumentTypeId)
                                .build())
                        .build())
                .container(container)
                .build());

        VesselProgrammingContainer vesselProgrammingContainer = VesselProgrammingContainer.builder()
                .catManifestedSize(Catalog.builder()
                        .id(catContainerSizeId)
                        .build())
                .catManifestedContainerType(Catalog.builder()
                        .id(catContainerTypeId)
                        .code("CT")
                        .build())
                .build();

        SystemRule systemRule = SystemRule.builder()
                .id(systemRuleId)
                .rule("[]")
                .build();

        HashMap<String, Object> defaultIsoCode = new HashMap<>(Map.of("iso_code_id", 1, "tare", 1, "payload", 1));

        VesselProgrammingContainer vesselProgrammingContainerCustomerReturn = VesselProgrammingContainer.builder()
                .id(vesselProgrammingContainerId)
                .demurrageDate(LocalDateTime.now())
                .catManifestedSize(Catalog.builder()
                        .id(catContainerSizeId)
                        .build())
                .catManifestedContainerType(Catalog.builder()
                        .id(catContainerTypeId)
                        .code("CT")
                        .build())
                .build();

        Eir equipmentEir = Eir.builder()
                .id(eirId)
                .businessUnit(BusinessUnit.builder().id(businessUnitId).build())
                .subBusinessUnit(BusinessUnit.builder().id(subBusinessUnitId).build())
                .localSubBusinessUnit(BusinessUnit.builder().id(input.getSubBusinessUnitLocalId()).build())
                .catMovement(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS)).build())
                .catEmptyFull(Catalog.builder().id(input.getEquipmentCatEmptyFullId()).build())
                .catOrigin(Optional.ofNullable(input.getMoveTypeId()).map(mt -> Catalog.builder().id(mt).build()).orElse(null))
                .truckArrivalDate(LocalDateTime.now())
                .vesselProgrammingDetail(Optional.ofNullable(1).map(pnd -> VesselProgrammingDetail.builder().id(pnd).build()).orElse(null))
                .container(Container.builder().id(container.getId()).build())
                .transportCompany(Company.builder().id(input.getTruckCompanyId()).build())
                .truck(Truck.builder().id(input.getVehicleId()).build())
                .driverPerson(Person.builder().id(input.getDriverId()).build())
                .shippingLine(Optional.ofNullable(1).map(sl -> ShippingLine.builder().id(sl).build()).orElse(null))
                .seal1("")
                .seal2("")
                .seal3("")
                .seal4("")
                .taraCnt(1)
                .cargoMaximumCnt(1)
                .isoCode(Optional.ofNullable(1).map(ic -> IsoCode.builder().id(ic).build()).orElse(null))
                .catContainerType(Optional.ofNullable(catalogIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS)).map(et -> Catalog.builder().id(et).build()).orElse(null))
                .catSizeCnt(Optional.ofNullable(catalogIds.get(CATALOG_SIZE_CONTAINER_40_ALIAS)).map(es -> Catalog.builder().id(es).build()).orElse(null))
                .catClassCnt(Catalog.builder().id(catalogIds.get(CATALOG_NO_GRADE)).build())
                .catTypeReefer(Optional.ofNullable(catReeferTypeId).map(rt -> Catalog.builder().id(rt).build()).orElse(null))
                .catEngineBrand(Optional.ofNullable(100).map(mm -> Catalog.builder().id(mm).build()).orElse(null))
                .dateManufacture(LocalDateTime.now())
                .observation(input.getComments())
                .structureWithDamage(false)
                .structureDamaged(false)
                .machineryWithDamage(false)
                .machineryDamaged(false)
                .controlRevision(null)
                .dateRevision(LocalDateTime.now())
                .controlAssignment(null)
                .dateGateOutInspection(LocalDateTime.now())
                .truckMultipleLoad(false)
                .truckRetreatWithLoad(false)
                .active(true)
                .catCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_GI_GENERAL_CREATION)).build())
                .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                .registrationDate(LocalDateTime.now())
                .clientCompany(Optional.ofNullable(1).map(cs -> Company.builder().id(cs).build()).orElse(null))
                .traceEir("ins_gi_general")
                .controlAssignmentLight(null)
                .weightGoods(null)
                .catMeasureWeight(Optional.ofNullable(catMeasureWeight).map(mwi -> Catalog.builder().id(mwi).build()).orElse(null))
                .catMeansTransport(Catalog.builder().id(catalogIds.get(CATALOG_MEANS_TRANSPORT)).build())
                .catMeasureTare(Optional.ofNullable(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).map(mt -> Catalog.builder().id(mt).build()).orElse(null))
                .catMeasurePayload(Optional.ofNullable(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).map(mp -> Catalog.builder().id(mp).build()).orElse(null))
                .transportPlanningDetailFull(Optional.ofNullable(0).map(pd -> TransportPlanningDetail.builder().id(pd).build()).orElse(null))
                .returnDateH(Optional.ofNullable(LocalDateTime.now().toString()).map(dd -> LocalDateTime.parse(dd)).orElse(null))
                .externalDocumentNumber("")
                .chassisNumber(input.getInputChassisNumber())
                .flagChassisStayed(input.getFlagChassisStayed())
                .flagOnSiteInspection(input.getInspectionSitu())
                .flagNoMaersk(true)
                .numberTwr(input.getTwrNumber())
                .build();

        EirDocumentCargoDetail createdEirDocumentCargoDetail = EirDocumentCargoDetail.builder()
                .id(eirDocumentCargoDetailId)
                .eir(equipmentEir)
                .cargoDocumentDetail(Optional.ofNullable(cargoDocumentDetailId).map(dcd -> CargoDocumentDetail.builder().id(dcd).build()).orElse(null))
                .active(true)
                .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                .registrationDate(LocalDateTime.now())
                .documentCargoReference("")
                .catCargoDocumentType(Optional.ofNullable(catCargoDocumentTypeId).map(cdt -> Catalog.builder().id(cdt).build()).orElse(null))
                .build();

        Object[][] apsValidation = {{"USCHI"}};

        BusinessUnit localsubBusinessUnit = BusinessUnit.builder()
                .id(input.getSubBusinessUnitLocalId())
                .businesUnitAlias(subBusinessUnitLocalAlias)
                .build();

        Catalog emptyContainerCatalog = Catalog.builder()
                .id(catEmptyContainerId)
                .alias("EMPTY")
                .build();

        Map<String, Object> loadedFiles = Map.of("estado", 1, "respuesta", "[{\"adjunto_id\":1233,\"nombre\":\"archivo1.jpg\",\"descripcion\":\"Sample description\",\"tipo\":1,\"accion\":\"N\",\"id\":\"0ec7f269-9322-4d02-8cbf-1747346c12c7\",\"tipo_adjunto_id\":30892,\"numero\":\"12345\"},{\"adjunto_id\":1234,\"nombre\":\"archivo2.png\",\"descripcion\":\"Another description\",\"tipo\":2,\"accion\":\"E\",\"id\":\"9yc7f269-9322-4d02-8cbf-1747346c1223\",\"tipo_adjunto_id\":30893,\"numero\":\"67890\"}]");

        when(businessUnitRepository.findParentBusinessUnitId(input.getSubBusinessUnitLocalId())).thenReturn(subBusinessUnitId);
        when(businessUnitRepository.findParentBusinessUnitId(subBusinessUnitId)).thenReturn(businessUnitId);
        when(catalogService.findIdsByAliases(any())).thenReturn(catalogIds);
        when(containerRepository.findById(equipmentInput.get(0).getEquipmentId())).thenReturn(Optional.of(container));
        when(gateInGeneralRegisterV2Repository.getActiveCargoDocumentByDetailId(equipmentInput.get(0).getDocumentoCargaDetalleId())).thenReturn(Optional.of(cargoDocumentDetail));
        when(gateInGeneralRegisterV2Repository.getVesselProgrammingContainer(pageable, vesselProgrammingDetailId, containerId)).thenReturn(Arrays.asList(vesselProgrammingContainer));
        when(systemRuleRepository.findByAliasAndActiveTrue("sds_subshippingline_equivalence")).thenReturn(systemRule);
        when(isoCodeRepository.getDefaultIsoCodeContainer(catContainerSizeId, catContainerTypeId)).thenReturn(defaultIsoCode);
        when(containerRepository.fnGetDefaultReeferType(input.getEquipment())).thenReturn(catReeferTypeId);
        when(gateInGeneralRegisterV2Repository.getVesselProgrammingContainer(pageable, equipmentInput.get(0).getProgramacionNaveDetalleId(), equipmentInput.get(0).getEquipmentId())).thenReturn(Arrays.asList(vesselProgrammingContainerCustomerReturn));
        when(systemRuleRepository.findByAliasAndBusinessUnitId("sde_tipo_mov_cita_obligario", businessUnitId, subBusinessUnitId)).thenReturn(Optional.empty());
        when(cargoDocumentDetailRepository.validateContainerStock(businessUnitId, subBusinessUnitId, catalogIds.get(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS), input.getEquipmentCatEmptyFullId(), container.getId(), input.getEquipment(), userRegistrationId, catalogIds.get(CATALOG_GI_GENERAL_CREATION), "adjust stock gate in", input.getLanguageId())).thenReturn("");
        when(eirRepository.save(any())).thenReturn(equipmentEir);
        when(eirDocumentCargoDetailRepository.save(any())).thenReturn(createdEirDocumentCargoDetail);
        when(attachmentRepository.loadFiles(userRegistrationId, equipmentInput.get(0).getAdjuntos().toString())).thenReturn(loadedFiles);
        when(businessUnitRepository.findById(input.getSubBusinessUnitLocalId())).thenReturn(null);
        when(systemRuleRepository.findByAliasAndActiveTrue(input.getSystemRuleId())).thenReturn(systemRule);
        when(messageLanguageService.getMessage("PRC_GI_GENERAL", 1, input.getLanguageId())).thenReturn("OK");
        when(businessUnitRepository.findById(input.getSubBusinessUnitLocalId())).thenReturn(Optional.of(localsubBusinessUnit));
        when(catalogRepository.findById(input.getEquipmentCatEmptyFullId())).thenReturn(Optional.of(emptyContainerCatalog));
        when(emrInspectionRepository.findByEirId(eirId)).thenReturn(Optional.empty());
        when(eirChassisZoneActivityRepository.findByEirChassisId(pageable, equipmentInput.get(0).getEirChassisId())).thenReturn(Arrays.asList());
        when(systemRuleRepository.gateinGeneralEquipmentAppointmentValidate(input.getSubBusinessUnitLocalId(), input.getEquipment())).thenReturn(apsValidation);

        //WHEN
        GateInGeneralRegisterV2Output.Output output = gateInGeneralRegisterV2Service.registerGateInTransactional(request);

        //THEN
        assertNotNull(output);
        assertNotNull(output.getResult());
        assertNotNull(output.getResponse());
        assertEquals(1, output.getResponse().size());
//        assertEquals(1, output.getResult().getResultState());
    }

    @Test
    void givenFullContainerRequest_whenRegisterGateIn_returnCreatedEir() throws Exception {
        //GIVEN
        HashMap<String, Integer> catalogIds = mockCatalogs();
        Integer subBusinessUnitLocalId = 3, driverId = 1, vehicleId = 1, truckCompanyId = 1, userRegistrationId = 1, languageId = 1,
                containerId = 1, isoCodeId = 1, cargoDocumentDetailId = 1, vesselProgrammingDetailId = 1, consigneeCompanyId = 2,
                shippingLineId = 1, systemRuleId = 1, vesselProgrammingContainerId = 1, eirId = 1, eirDocumentCargoDetailId = 1,
                transportPlanningDetailId = 1;
        String containerNumber = "TFHC4000000", catContainerTypeCode = "IC", subBusinessUnitLocalAlias = "ALIAS";
        Integer catEngineBrandId = 100, catReeferTypeId = 101, catCargoDocumentTypeId = 103, catContainerSizeId = catalogIds.get(CATALOG_SIZE_CONTAINER_40_ALIAS),
                catContainerTypeId = catalogIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS), catMeasureWeight = 105,
                catEmptyContainerId = catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS), catContainerTypeFamily = 1;
        Integer subBusinessUnitId = 2, businessUnitId = 1;
        Pageable pageable = PageRequest.of(0, 1);

        GateInGeneralRegisterV2Input.Input input = GateInGeneralRegisterV2Input.Input.builder()
                .subBusinessUnitLocalId(subBusinessUnitLocalId)
                .equipmentCatEmptyFullId(catalogIds.get(CATALOG_TYPE_PROCESS_IS_FULL_ALIAS))
                .equipment("[{\"programacion_nave_detalle_id\":1,\"documento_carga_detalle_id\":1,\"planning_detail_id\":1,\"equipment_id\":1,\"equipment_number\":\"TFHC4000140\",\"iso_code_id\":1,\"manifest_weight\":1,\"manifest_weight_unit_id\":" + catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS).toString() + ",\"temperature\":\"\",\"temperature_unit_id\":null,\"seal_1\":\"1\",\"seal_2\":\"\",\"seal_3\":\"\",\"seal_4\":\"\",\"reefer_connect\":null,\"reefer_dangerous\":null,\"imos\":[],\"description_dangerous_substance\":\"\",\"pictures\":[{\"id\":\"262a3171-3692-4af7-a461-3376ae25a286\",\"nombre\":\"foto_1\",\"formato\":\"jpg\",\"peso\":97263,\"ubicacion\":\"Gate/Gate_In/\",\"tipo_adjunto\":48028,\"accion\":\"N\",\"url\":\"https://maerskapmtisstgsd1prep.blob.core.windows.net/sdg/Gate%2FGate_In%2F262a3171-3692-4af7-a461-3376ae25a286?sv=2021-06-08&ss=bfqt&srt=sco&sp=rwdlacupiytfx&se=2029-01-06T22:39:50Z&st=2023-01-06T14:39:50Z&spr=https&sig=702KS74M87UyeusjEaLNMAMe15IrglRqDpA7x%2BJwAZ4%3D\"},{\"id\":\"5b21ef65-5621-4736-9287-c1c2ae7e33f3\",\"nombre\":\"foto_2\",\"formato\":\"jpg\",\"peso\":97263,\"ubicacion\":\"Gate/Gate_In/\",\"tipo_adjunto\":48028,\"accion\":\"N\",\"url\":\"https://maerskapmtisstgsd1prep.blob.core.windows.net/sdg/Gate%2FGate_In%2F5b21ef65-5621-4736-9287-c1c2ae7e33f3?sv=2021-06-08&ss=bfqt&srt=sco&sp=rwdlacupiytfx&se=2029-01-06T22:39:50Z&st=2023-01-06T14:39:50Z&spr=https&sig=702KS74M87UyeusjEaLNMAMe15IrglRqDpA7x%2BJwAZ4%3D\"}],\"aps_id\":\"0\",\"no_maersk\":0}]")
                .moveTypeId(null)
                .chassis("{}")
                .driverId(driverId)
                .vehicleId(vehicleId)
                .truckCompanyId(truckCompanyId)
                .comments("")
                .userRegistrationId(userRegistrationId)
                .languageId(languageId)
                .operationCode("STRFLL")
                .inputChassisNumber("")
                .flagChassisStayed(false)
                .inspectionSitu(true)
                .twrNumber("")
                .systemRuleId("sd1_rule_integration_sdy")
                .build();

        GateInGeneralRegisterV2Input.Root request = GateInGeneralRegisterV2Input.Root.builder()
                .prefix(GateInGeneralRegisterV2Input.Prefix.builder()
                        .input(input)
                        .build())
                .build();

        Gson gson = new Gson();
        Type equipmentListType = new TypeToken<ArrayList<GateInGeneralRegisterV2Equipment>>() {
        }.getType();
        Type chassisListType = new TypeToken<GateInGeneralRegisterV2Chassis>() {
        }.getType();
        List<GateInGeneralRegisterV2Equipment> equipmentInput = gson.fromJson(input.getEquipment(), equipmentListType);
        GateInGeneralRegisterV2Chassis chassisInput = gson.fromJson(input.getChassis(), chassisListType);

        Container container = Container.builder()
                .id(containerId)
                .containerNumber(containerNumber)
                .containerTare(0)
                .catEquipMeasureTare(Catalog.builder().id(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).build())
                .maximunPayload(1)
                .catEquipMeasurePayload(Catalog.builder().id(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).build())
                .catGrade(null)
                .manufactureDate(LocalDateTime.now())
                .catEngineBrand(Catalog.builder().id(catEngineBrandId).build())
                .catReeferType(Catalog.builder().id(catReeferTypeId).build())
                .containerNumber(input.getEquipment())
                .isoCode(IsoCode.builder().id(isoCodeId).build())
                .catSize(Catalog.builder().id(catalogIds.get(CATALOG_SIZE_CONTAINER_40_ALIAS)).build())
                .catContainerType(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS)).build())
                .build();

        CargoDocumentDetail cargoDocumentDetail = (CargoDocumentDetail.builder()
                .cargoDocument(CargoDocument.builder()
                        .id(cargoDocumentDetailId)
                        .vesselProgrammingDetail(VesselProgrammingDetail.builder()
                                .id(vesselProgrammingDetailId)
                                .build())
                        .consigneeCompany(Company.builder()
                                .id(consigneeCompanyId)
                                .build())
                        .shippingLine(ShippingLine.builder()
                                .id(shippingLineId)
                                .build())
                        .cargoDocument("CD")
                        .catCargoDocumentType(Catalog.builder()
                                .id(catCargoDocumentTypeId)
                                .build())
                        .build())
                .container(container)
                .build());

        VesselProgrammingContainer vesselProgrammingContainer = VesselProgrammingContainer.builder()
                .catManifestedSize(Catalog.builder()
                        .id(catContainerSizeId)
                        .build())
                .catManifestedContainerType(Catalog.builder()
                        .id(catContainerTypeId)
                        .code("CT")
                        .build())
                .build();

        SystemRule systemRule = SystemRule.builder()
                .id(systemRuleId)
                .rule("[]")
                .build();

        HashMap<String, Object> defaultIsoCode = new HashMap<>(Map.of("iso_code_id", 1, "tare", 1, "payload", 1));

        VesselProgrammingContainer vesselProgrammingContainerCustomerReturn = VesselProgrammingContainer.builder()
                .id(vesselProgrammingContainerId)
                .demurrageDate(LocalDateTime.now())
                .catManifestedSize(Catalog.builder()
                        .id(catContainerSizeId)
                        .build())
                .catManifestedContainerType(Catalog.builder()
                        .id(catContainerTypeId)
                        .code("CT")
                        .build())
                .build();

        Eir equipmentEir = Eir.builder()
                .id(eirId)
                .businessUnit(BusinessUnit.builder().id(businessUnitId).build())
                .subBusinessUnit(BusinessUnit.builder().id(subBusinessUnitId).build())
                .localSubBusinessUnit(BusinessUnit.builder().id(input.getSubBusinessUnitLocalId()).build())
                .catMovement(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS)).build())
                .catEmptyFull(Catalog.builder().id(input.getEquipmentCatEmptyFullId()).build())
                .catOrigin(Optional.ofNullable(input.getMoveTypeId()).map(mt -> Catalog.builder().id(mt).build()).orElse(null))
                .truckArrivalDate(LocalDateTime.now())
                .vesselProgrammingDetail(Optional.ofNullable(1).map(pnd -> VesselProgrammingDetail.builder().id(pnd).build()).orElse(null))
                .container(Container.builder().id(container.getId()).build())
                .transportCompany(Company.builder().id(input.getTruckCompanyId()).build())
                .truck(Truck.builder().id(input.getVehicleId()).build())
                .driverPerson(Person.builder().id(input.getDriverId()).build())
                .shippingLine(Optional.ofNullable(1).map(sl -> ShippingLine.builder().id(sl).build()).orElse(null))
                .seal1("")
                .seal2("")
                .seal3("")
                .seal4("")
                .taraCnt(1)
                .cargoMaximumCnt(1)
                .isoCode(Optional.ofNullable(1).map(ic -> IsoCode.builder().id(ic).build()).orElse(null))
                .catContainerType(Optional.ofNullable(catalogIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS)).map(et -> Catalog.builder().id(et).build()).orElse(null))
                .catSizeCnt(Optional.ofNullable(catalogIds.get(CATALOG_SIZE_CONTAINER_40_ALIAS)).map(es -> Catalog.builder().id(es).build()).orElse(null))
                .catClassCnt(Catalog.builder().id(catalogIds.get(CATALOG_NO_GRADE)).build())
                .catTypeReefer(Optional.ofNullable(catReeferTypeId).map(rt -> Catalog.builder().id(rt).build()).orElse(null))
                .catEngineBrand(Optional.ofNullable(100).map(mm -> Catalog.builder().id(mm).build()).orElse(null))
                .dateManufacture(LocalDateTime.now())
                .observation(input.getComments())
                .structureWithDamage(false)
                .structureDamaged(false)
                .machineryWithDamage(false)
                .machineryDamaged(false)
                .controlRevision(null)
                .dateRevision(LocalDateTime.now())
                .controlAssignment(null)
                .dateGateOutInspection(LocalDateTime.now())
                .truckMultipleLoad(false)
                .truckRetreatWithLoad(false)
                .active(true)
                .catCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_GI_GENERAL_CREATION)).build())
                .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                .registrationDate(LocalDateTime.now())
                .clientCompany(Optional.ofNullable(1).map(cs -> Company.builder().id(cs).build()).orElse(null))
                .traceEir("ins_gi_general")
                .controlAssignmentLight(null)
                .weightGoods(null)
                .catMeasureWeight(Optional.ofNullable(catMeasureWeight).map(mwi -> Catalog.builder().id(mwi).build()).orElse(null))
                .catMeansTransport(Catalog.builder().id(catalogIds.get(CATALOG_MEANS_TRANSPORT)).build())
                .catMeasureTare(Optional.ofNullable(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).map(mt -> Catalog.builder().id(mt).build()).orElse(null))
                .catMeasurePayload(Optional.ofNullable(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).map(mp -> Catalog.builder().id(mp).build()).orElse(null))
                .transportPlanningDetailFull(Optional.ofNullable(0).map(pd -> TransportPlanningDetail.builder().id(pd).build()).orElse(null))
                .returnDateH(Optional.ofNullable(LocalDateTime.now().toString()).map(dd -> LocalDateTime.parse(dd)).orElse(null))
                .externalDocumentNumber("")
                .chassisNumber(input.getInputChassisNumber())
                .flagChassisStayed(input.getFlagChassisStayed())
                .flagOnSiteInspection(input.getInspectionSitu())
                .flagNoMaersk(true)
                .numberTwr(input.getTwrNumber())
                .build();

        EirDocumentCargoDetail createdEirDocumentCargoDetail = EirDocumentCargoDetail.builder()
                .id(eirDocumentCargoDetailId)
                .eir(equipmentEir)
                .cargoDocumentDetail(Optional.ofNullable(cargoDocumentDetailId).map(dcd -> CargoDocumentDetail.builder().id(dcd).build()).orElse(null))
                .active(true)
                .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                .registrationDate(LocalDateTime.now())
                .documentCargoReference("")
                .catCargoDocumentType(Optional.ofNullable(catCargoDocumentTypeId).map(cdt -> Catalog.builder().id(cdt).build()).orElse(null))
                .build();

        EmrInspection emrInspection = EmrInspection.builder()
                .businessUnit(BusinessUnit.builder().id(businessUnitId).build())
                .subBusinessUnit(BusinessUnit.builder().id(subBusinessUnitId).build())
                .eir(equipmentEir)
                .catStatus(Catalog.builder().id(catalogIds.get(CATALOG_PENDING_INSPECTION)).build())
                .active(true)
                .registrationDate(LocalDateTime.now())
                .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                .damageCount(0)
                .flagDamageBox(false)
                .flagDamageMachine(false)
                .build();

        TransportPlanningDetail transportPlanningDetail = TransportPlanningDetail.builder()
                .id(transportPlanningDetailId)
                .build();

        IsoCode isoCode = IsoCode.builder()
                .id(isoCodeId)
                .catSize(Catalog.builder().id(catContainerSizeId).build())
                .catContainerType(Catalog.builder()
                        .id(catContainerTypeId)
                        .variable3(catContainerTypeFamily)
                        .code(catContainerTypeCode)
                        .build())
                .build();

        Object[][] apsValidation = {{"USCHI"}};

        BusinessUnit localsubBusinessUnit = BusinessUnit.builder()
                .id(input.getSubBusinessUnitLocalId())
                .businesUnitAlias(subBusinessUnitLocalAlias)
                .build();

        Catalog emptyContainerCatalog = Catalog.builder()
                .id(catEmptyContainerId)
                .alias("EMPTY")
                .build();

        Map<String, Object> loadedFiles = Map.of("estado", 1, "respuesta", "[{\"adjunto_id\":1233,\"nombre\":\"archivo1.jpg\",\"descripcion\":\"Sample description\",\"tipo\":1,\"accion\":\"N\",\"id\":\"0ec7f269-9322-4d02-8cbf-1747346c12c7\",\"tipo_adjunto_id\":30892,\"numero\":\"12345\"},{\"adjunto_id\":1234,\"nombre\":\"archivo2.png\",\"descripcion\":\"Another description\",\"tipo\":2,\"accion\":\"E\",\"id\":\"9yc7f269-9322-4d02-8cbf-1747346c1223\",\"tipo_adjunto_id\":30893,\"numero\":\"67890\"}]");

        when(businessUnitRepository.findParentBusinessUnitId(input.getSubBusinessUnitLocalId())).thenReturn(subBusinessUnitId);
        when(businessUnitRepository.findParentBusinessUnitId(subBusinessUnitId)).thenReturn(businessUnitId);
        when(catalogService.findIdsByAliases(any())).thenReturn(catalogIds);
        when(containerRepository.findById(equipmentInput.get(0).getEquipmentId())).thenReturn(Optional.of(container));
        when(isoCodeRepository.findById(equipmentInput.get(0).getIsoCodeId())).thenReturn(Optional.of(isoCode));
        when(containerRepository.fnGetDefaultReeferType(input.getEquipment())).thenReturn(catReeferTypeId);
        when(isoCodeRepository.getDefaultIsoCodeContainer(catContainerSizeId, catContainerTypeId)).thenReturn(defaultIsoCode);
        when(gateInGeneralRegisterV2Repository.getActiveCargoDocumentByDetailId(equipmentInput.get(0).getDocumentoCargaDetalleId())).thenReturn(Optional.of(cargoDocumentDetail));
        when(gateInGeneralRegisterV2Repository.getVesselProgrammingContainer(pageable, vesselProgrammingDetailId, containerId)).thenReturn(Arrays.asList(vesselProgrammingContainer));
        when(systemRuleRepository.findByAliasAndActiveTrue("sds_subshippingline_equivalence")).thenReturn(systemRule);
        when(cargoDocumentDetailRepository.validateContainerStock(businessUnitId, subBusinessUnitId, catalogIds.get(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS), input.getEquipmentCatEmptyFullId(), container.getId(), input.getEquipment(), userRegistrationId, catalogIds.get(CATALOG_GI_GENERAL_CREATION), "adjust stock gate in", input.getLanguageId())).thenReturn("");
        when(eirRepository.save(any())).thenReturn(equipmentEir);
        when(eirDocumentCargoDetailRepository.save(any())).thenReturn(createdEirDocumentCargoDetail);
        when(gateInGeneralRegisterV2Repository.getVesselProgrammingContainer(pageable, equipmentInput.get(0).getProgramacionNaveDetalleId(), equipmentInput.get(0).getEquipmentId())).thenReturn(Arrays.asList(vesselProgrammingContainerCustomerReturn));
        when(emrInspectionRepository.save(any())).thenReturn(emrInspection);
        when(transportPlanningDetailRepository.findById(equipmentInput.get(0).getPlanningDetailId())).thenReturn(Optional.of(transportPlanningDetail));
        when(attachmentRepository.loadFiles(userRegistrationId, equipmentInput.get(0).getAdjuntos().toString())).thenReturn(loadedFiles);
        when(businessUnitRepository.findById(input.getSubBusinessUnitLocalId())).thenReturn(Optional.of(localsubBusinessUnit));
        when(systemRuleRepository.findByAliasAndActiveTrue(input.getSystemRuleId())).thenReturn(systemRule);
        when(messageLanguageService.getMessage("PRC_GI_GENERAL", 1, input.getLanguageId())).thenReturn("OK");
        when(catalogRepository.findById(input.getEquipmentCatEmptyFullId())).thenReturn(Optional.of(emptyContainerCatalog));
        when(emrInspectionRepository.findByEirId(eirId)).thenReturn(Optional.empty());
        when(eirChassisZoneActivityRepository.findByEirChassisId(pageable, equipmentInput.get(0).getEirChassisId())).thenReturn(Arrays.asList());
        when(systemRuleRepository.gateinGeneralEquipmentAppointmentValidate(input.getSubBusinessUnitLocalId(), input.getEquipment())).thenReturn(apsValidation);

        //WHEN
        GateInGeneralRegisterV2Output.Output output = gateInGeneralRegisterV2Service.registerGateInTransactional(request);

        //THEN
        assertNotNull(output);
        assertNotNull(output.getResult());
        assertNotNull(output.getResponse());
        assertEquals(1, output.getResponse().size());
//        assertEquals(1, output.getResult().getResultState());
    }

    @Test
    void givenTwoEmptyContainersRequest_whenRegisterGateIn_returnCreatedEirs() throws Exception {
        //GIVEN
        HashMap<String, Integer> catalogIds = mockCatalogs();
        Integer subBusinessUnitLocalId = 3, driverId = 1, vehicleId = 1, truckCompanyId = 1, userRegistrationId = 1, languageId = 1,
                containerId = 1, isoCodeId = 1, cargoDocumentDetailId = 1, vesselProgrammingDetailId = 1, consigneeCompanyId = 2,
                shippingLineId = 1, systemRuleId = 1, vesselProgrammingContainerId = 1, eirId = 1, eirDocumentCargoDetailId = 1;
        String subBusinessUnitLocalAlias = "ALIAS";
        Integer catEngineBrandId = 100, catReeferTypeId = 101, catCargoDocumentTypeId = 103, catContainerSizeId = catalogIds.get(CATALOG_SIZE_CONTAINER_20_ALIAS),
                catContainerTypeId = catalogIds.get(CATALOG_TYPE_CONTAINER_DRY_ALIAS), catMeasureWeight = 105, catEmptyContainerId = catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS);
        Integer subBusinessUnitId = 2, businessUnitId = 1;
        Pageable pageable = PageRequest.of(0, 1);

        GateInGeneralRegisterV2Input.Input input = GateInGeneralRegisterV2Input.Input.builder()
                .subBusinessUnitLocalId(subBusinessUnitLocalId)
                .equipmentCatEmptyFullId(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS))
                .equipment("[{\"programacion_nave_detalle_id\":1,\"documento_carga_detalle_id\":1,\"planning_detail_id\":null,\"equipment_id\":1,\"equipment_number\":\"TEHC4000000\",\"iso_code_id\":1,\"manifest_weight\":null,\"manifest_weight_unit_id\":null,\"temperature\":\"\",\"temperature_unit_id\":null,\"seal_1\":\"\",\"seal_2\":\"\",\"seal_3\":\"\",\"seal_4\":\"\",\"reefer_connect\":null,\"reefer_dangerous\":null,\"imos\":[],\"description_dangerous_substance\":\"\",\"pictures\":[],\"aps_id\":\"0\",\"no_maersk\":1}, " +
                        "{\"programacion_nave_detalle_id\":2,\"documento_carga_detalle_id\":2,\"planning_detail_id\":null,\"equipment_id\":2,\"equipment_number\":\"TEHC4000001\",\"iso_code_id\":1,\"manifest_weight\":null,\"manifest_weight_unit_id\":null,\"temperature\":\"\",\"temperature_unit_id\":null,\"seal_1\":\"\",\"seal_2\":\"\",\"seal_3\":\"\",\"seal_4\":\"\",\"reefer_connect\":null,\"reefer_dangerous\":null,\"imos\":[],\"description_dangerous_substance\":\"\",\"pictures\":[],\"aps_id\":\"0\",\"no_maersk\":1}]")
                .moveTypeId(catalogIds.get(CATALOG_CUSTOMER_RETURN))
                .chassis("{}")
                .driverId(driverId)
                .vehicleId(vehicleId)
                .truckCompanyId(truckCompanyId)
                .comments("")
                .userRegistrationId(userRegistrationId)
                .languageId(languageId)
                .operationCode("DEVCLNTE")
                .inputChassisNumber("")
                .flagChassisStayed(false)
                .inspectionSitu(true)
                .twrNumber("")
                .systemRuleId("sd1_rule_integration_sdy")
                .build();

        GateInGeneralRegisterV2Input.Root request = GateInGeneralRegisterV2Input.Root.builder()
                .prefix(GateInGeneralRegisterV2Input.Prefix.builder()
                        .input(input)
                        .build())
                .build();

        Gson gson = new Gson();
        Type equipmentListType = new TypeToken<ArrayList<GateInGeneralRegisterV2Equipment>>() {
        }.getType();

        List<GateInGeneralRegisterV2Equipment> equipmentInput = gson.fromJson(input.getEquipment(), equipmentListType);

        Container firstContainer = Container.builder()
                .id(equipmentInput.get(0).getEquipmentId())
                .containerNumber(equipmentInput.get(0).getEquipmentNumber())
                .containerTare(0)
                .catEquipMeasureTare(Catalog.builder().id(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).build())
                .maximunPayload(1)
                .catEquipMeasurePayload(Catalog.builder().id(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).build())
                .catGrade(null)
                .manufactureDate(LocalDateTime.now())
                .catEngineBrand(Catalog.builder().id(catEngineBrandId).build())
                .catReeferType(Catalog.builder().id(catReeferTypeId).build())
                .containerNumber(equipmentInput.get(0).getEquipmentNumber())
                .isoCode(IsoCode.builder().id(isoCodeId).build())
                .catSize(Catalog.builder().id(catalogIds.get(CATALOG_SIZE_CONTAINER_20_ALIAS)).build())
                .catContainerType(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS)).build())
                .build();

        Container secondContainer = Container.builder()
                .id(equipmentInput.get(1).getEquipmentId())
                .containerNumber(equipmentInput.get(1).getEquipmentNumber())
                .containerTare(1)
                .catEquipMeasureTare(Catalog.builder().id(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).build())
                .maximunPayload(1)
                .catEquipMeasurePayload(Catalog.builder().id(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).build())
                .catGrade(null)
                .manufactureDate(LocalDateTime.now())
                .catEngineBrand(Catalog.builder().id(catEngineBrandId).build())
                .catReeferType(Catalog.builder().id(catReeferTypeId).build())
                .containerNumber(equipmentInput.get(1).getEquipmentNumber())
                .isoCode(IsoCode.builder().id(isoCodeId).build())
                .catSize(Catalog.builder().id(catalogIds.get(CATALOG_SIZE_CONTAINER_20_ALIAS)).build())
                .catContainerType(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS)).build())
                .build();

        CargoDocumentDetail firstCargoDocumentDetail = (CargoDocumentDetail.builder()
                .cargoDocument(CargoDocument.builder()
                        .id(cargoDocumentDetailId)
                        .vesselProgrammingDetail(VesselProgrammingDetail.builder()
                                .id(vesselProgrammingDetailId)
                                .build())
                        .consigneeCompany(Company.builder()
                                .id(consigneeCompanyId)
                                .build())
                        .shippingLine(ShippingLine.builder()
                                .id(shippingLineId)
                                .build())
                        .cargoDocument("CD")
                        .catCargoDocumentType(Catalog.builder()
                                .id(catCargoDocumentTypeId)
                                .build())
                        .build())
                .container(firstContainer)
                .build());

        CargoDocumentDetail secondCargoDocumentDetail = (CargoDocumentDetail.builder()
                .cargoDocument(CargoDocument.builder()
                        .id(cargoDocumentDetailId)
                        .vesselProgrammingDetail(VesselProgrammingDetail.builder()
                                .id(vesselProgrammingDetailId)
                                .build())
                        .consigneeCompany(Company.builder()
                                .id(consigneeCompanyId)
                                .build())
                        .shippingLine(ShippingLine.builder()
                                .id(shippingLineId)
                                .build())
                        .cargoDocument("CD")
                        .catCargoDocumentType(Catalog.builder()
                                .id(catCargoDocumentTypeId)
                                .build())
                        .build())
                .container(secondContainer)
                .build());

        VesselProgrammingContainer vesselProgrammingContainer = VesselProgrammingContainer.builder()
                .catManifestedSize(Catalog.builder()
                        .id(catContainerSizeId)
                        .build())
                .catManifestedContainerType(Catalog.builder()
                        .id(catContainerTypeId)
                        .code("CT")
                        .build())
                .build();

        SystemRule systemRule = SystemRule.builder()
                .id(systemRuleId)
                .rule("[]")
                .build();

        HashMap<String, Object> defaultIsoCode = new HashMap<>(Map.of("iso_code_id", 1, "tare", 1, "payload", 1));

        VesselProgrammingContainer vesselProgrammingContainerCustomerReturn = VesselProgrammingContainer.builder()
                .id(vesselProgrammingContainerId)
                .demurrageDate(LocalDateTime.now())
                .catManifestedSize(Catalog.builder()
                        .id(catContainerSizeId)
                        .build())
                .catManifestedContainerType(Catalog.builder()
                        .id(catContainerTypeId)
                        .code("CT")
                        .build())
                .build();

        Eir equipmentEir = Eir.builder()
                .id(eirId)
                .businessUnit(BusinessUnit.builder().id(businessUnitId).build())
                .subBusinessUnit(BusinessUnit.builder().id(subBusinessUnitId).build())
                .localSubBusinessUnit(BusinessUnit.builder().id(input.getSubBusinessUnitLocalId()).build())
                .catMovement(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS)).build())
                .catEmptyFull(Catalog.builder().id(input.getEquipmentCatEmptyFullId()).build())
                .catOrigin(Optional.ofNullable(input.getMoveTypeId()).map(mt -> Catalog.builder().id(mt).build()).orElse(null))
                .truckArrivalDate(LocalDateTime.now())
                .vesselProgrammingDetail(Optional.ofNullable(1).map(pnd -> VesselProgrammingDetail.builder().id(pnd).build()).orElse(null))
                .container(Container.builder().id(firstContainer.getId()).build())
                .transportCompany(Company.builder().id(input.getTruckCompanyId()).build())
                .truck(Truck.builder().id(input.getVehicleId()).build())
                .driverPerson(Person.builder().id(input.getDriverId()).build())
                .shippingLine(Optional.ofNullable(1).map(sl -> ShippingLine.builder().id(sl).build()).orElse(null))
                .seal1("")
                .seal2("")
                .seal3("")
                .seal4("")
                .taraCnt(1)
                .cargoMaximumCnt(1)
                .isoCode(Optional.ofNullable(1).map(ic -> IsoCode.builder().id(ic).build()).orElse(null))
                .catContainerType(Optional.ofNullable(catalogIds.get(CATALOG_TYPE_CONTAINER_HC_ALIAS)).map(et -> Catalog.builder().id(et).build()).orElse(null))
                .catSizeCnt(Optional.ofNullable(catalogIds.get(CATALOG_SIZE_CONTAINER_20_ALIAS)).map(es -> Catalog.builder().id(es).build()).orElse(null))
                .catClassCnt(Catalog.builder().id(catalogIds.get(CATALOG_NO_GRADE)).build())
                .catTypeReefer(Optional.ofNullable(catReeferTypeId).map(rt -> Catalog.builder().id(rt).build()).orElse(null))
                .catEngineBrand(Optional.ofNullable(100).map(mm -> Catalog.builder().id(mm).build()).orElse(null))
                .dateManufacture(LocalDateTime.now())
                .observation(input.getComments())
                .structureWithDamage(false)
                .structureDamaged(false)
                .machineryWithDamage(false)
                .machineryDamaged(false)
                .controlRevision(null)
                .dateRevision(LocalDateTime.now())
                .controlAssignment(null)
                .dateGateOutInspection(LocalDateTime.now())
                .truckMultipleLoad(false)
                .truckRetreatWithLoad(false)
                .active(true)
                .catCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_GI_GENERAL_CREATION)).build())
                .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                .registrationDate(LocalDateTime.now())
                .clientCompany(Optional.ofNullable(1).map(cs -> Company.builder().id(cs).build()).orElse(null))
                .traceEir("ins_gi_general")
                .controlAssignmentLight(null)
                .weightGoods(null)
                .catMeasureWeight(Optional.ofNullable(catMeasureWeight).map(mwi -> Catalog.builder().id(mwi).build()).orElse(null))
                .catMeansTransport(Catalog.builder().id(catalogIds.get(CATALOG_MEANS_TRANSPORT)).build())
                .catMeasureTare(Optional.ofNullable(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).map(mt -> Catalog.builder().id(mt).build()).orElse(null))
                .catMeasurePayload(Optional.ofNullable(catalogIds.get(CATALOG_MEASURE_WEIGHT_KG_ALIAS)).map(mp -> Catalog.builder().id(mp).build()).orElse(null))
                .transportPlanningDetailFull(Optional.ofNullable(0).map(pd -> TransportPlanningDetail.builder().id(pd).build()).orElse(null))
                .returnDateH(Optional.ofNullable(LocalDateTime.now().toString()).map(dd -> LocalDateTime.parse(dd)).orElse(null))
                .externalDocumentNumber("")
                .chassisNumber(input.getInputChassisNumber())
                .flagChassisStayed(input.getFlagChassisStayed())
                .flagOnSiteInspection(input.getInspectionSitu())
                .flagNoMaersk(true)
                .numberTwr(input.getTwrNumber())
                .build();

        EirDocumentCargoDetail createdEirDocumentCargoDetail = EirDocumentCargoDetail.builder()
                .id(eirDocumentCargoDetailId)
                .eir(equipmentEir)
                .cargoDocumentDetail(Optional.ofNullable(cargoDocumentDetailId).map(dcd -> CargoDocumentDetail.builder().id(dcd).build()).orElse(null))
                .active(true)
                .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                .registrationDate(LocalDateTime.now())
                .documentCargoReference("")
                .catCargoDocumentType(Optional.ofNullable(catCargoDocumentTypeId).map(cdt -> Catalog.builder().id(cdt).build()).orElse(null))
                .build();

        Object[][] apsValidation = {{"USCHI"}};

        BusinessUnit localsubBusinessUnit = BusinessUnit.builder()
                .id(input.getSubBusinessUnitLocalId())
                .businesUnitAlias(subBusinessUnitLocalAlias)
                .build();

        Catalog emptyContainerCatalog = Catalog.builder()
                .id(catEmptyContainerId)
                .alias("EMPTY")
                .build();

        when(businessUnitRepository.findParentBusinessUnitId(input.getSubBusinessUnitLocalId())).thenReturn(subBusinessUnitId);
        when(businessUnitRepository.findParentBusinessUnitId(subBusinessUnitId)).thenReturn(businessUnitId);
        when(catalogService.findIdsByAliases(any())).thenReturn(catalogIds);
        when(containerRepository.findById(equipmentInput.get(0).getEquipmentId())).thenReturn(Optional.of(firstContainer));
        when(gateInGeneralRegisterV2Repository.getActiveCargoDocumentByDetailId(equipmentInput.get(0).getDocumentoCargaDetalleId())).thenReturn(Optional.of(firstCargoDocumentDetail));
        when(containerRepository.findById(equipmentInput.get(1).getEquipmentId())).thenReturn(Optional.of(secondContainer));
        when(gateInGeneralRegisterV2Repository.getActiveCargoDocumentByDetailId(equipmentInput.get(1).getDocumentoCargaDetalleId())).thenReturn(Optional.of(secondCargoDocumentDetail));
        when(gateInGeneralRegisterV2Repository.getVesselProgrammingContainer(pageable, vesselProgrammingDetailId, containerId)).thenReturn(Arrays.asList(vesselProgrammingContainer));
        when(systemRuleRepository.findByAliasAndActiveTrue("sds_subshippingline_equivalence")).thenReturn(systemRule);
        when(isoCodeRepository.getDefaultIsoCodeContainer(catContainerSizeId, catContainerTypeId)).thenReturn(defaultIsoCode);
        when(containerRepository.fnGetDefaultReeferType(input.getEquipment())).thenReturn(catReeferTypeId);
        when(gateInGeneralRegisterV2Repository.getVesselProgrammingContainer(pageable, equipmentInput.get(0).getProgramacionNaveDetalleId(), equipmentInput.get(0).getEquipmentId())).thenReturn(Arrays.asList(vesselProgrammingContainerCustomerReturn));
        when(gateInGeneralRegisterV2Repository.getVesselProgrammingContainer(pageable, equipmentInput.get(1).getProgramacionNaveDetalleId(), equipmentInput.get(1).getEquipmentId())).thenReturn(Arrays.asList(vesselProgrammingContainerCustomerReturn));
        when(systemRuleRepository.findByAliasAndBusinessUnitId("sde_tipo_mov_cita_obligario", businessUnitId, subBusinessUnitId)).thenReturn(Optional.empty());
        when(cargoDocumentDetailRepository.validateContainerStock(businessUnitId, subBusinessUnitId, catalogIds.get(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS), input.getEquipmentCatEmptyFullId(), firstContainer.getId(), equipmentInput.get(0).getEquipmentNumber(), userRegistrationId, catalogIds.get(CATALOG_GI_GENERAL_CREATION), "adjust stock gate in", input.getLanguageId())).thenReturn("");
        when(cargoDocumentDetailRepository.validateContainerStock(businessUnitId, subBusinessUnitId, catalogIds.get(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS), input.getEquipmentCatEmptyFullId(), secondContainer.getId(), equipmentInput.get(1).getEquipmentNumber(), userRegistrationId, catalogIds.get(CATALOG_GI_GENERAL_CREATION), "adjust stock gate in", input.getLanguageId())).thenReturn("");
        when(eirRepository.save(any())).thenReturn(equipmentEir);
        when(eirDocumentCargoDetailRepository.save(any())).thenReturn(createdEirDocumentCargoDetail);
        when(businessUnitRepository.findById(input.getSubBusinessUnitLocalId())).thenReturn(null);
        when(systemRuleRepository.findByAliasAndActiveTrue(input.getSystemRuleId())).thenReturn(SystemRule.builder().id(2).rule("{}").build());
        when(businessUnitRepository.findById(input.getSubBusinessUnitLocalId())).thenReturn(Optional.of(localsubBusinessUnit));
        when(systemRuleRepository.findByAliasAndActiveTrue(input.getSystemRuleId())).thenReturn(systemRule);
        when(messageLanguageService.getMessage("PRC_GI_GENERAL", 1, input.getLanguageId())).thenReturn("OK");
        when(eirRepository.findById(anyInt())).thenReturn(Optional.of(equipmentEir));
        when(catalogRepository.findById(input.getEquipmentCatEmptyFullId())).thenReturn(Optional.of(emptyContainerCatalog));
        when(emrInspectionRepository.findByEirId(anyInt())).thenReturn(Optional.empty());
        when(eirChassisZoneActivityRepository.findByEirChassisId(pageable, equipmentInput.get(0).getEirChassisId())).thenReturn(Arrays.asList());
        when(eirChassisZoneActivityRepository.findByEirChassisId(pageable, equipmentInput.get(1).getEirChassisId())).thenReturn(Arrays.asList());
        when(systemRuleRepository.gateinGeneralEquipmentAppointmentValidate(input.getSubBusinessUnitLocalId(), input.getEquipment())).thenReturn(apsValidation);

        //WHEN
        GateInGeneralRegisterV2Output.Output output = gateInGeneralRegisterV2Service.registerGateInTransactional(request);

        //THEN
        assertNotNull(output);
        assertNotNull(output.getResult());
        assertNotNull(output.getResponse());
        assertEquals(2, output.getResponse().size());
    }
    @Test
    void givenOnlyChassisRequest_whenRegisterGateIn_returnDummyEir() throws Exception {
        // GIVEN
        HashMap<String, Integer> catalogIds = mockCatalogs();
        Integer subBusinessUnitLocalId = 3, driverId = 1, vehicleId = 1, truckCompanyId = 1, userRegistrationId = 1, languageId = 1;
        String subBusinessUnitLocalAlias = "ALIAS";
        Integer subBusinessUnitId = 2, businessUnitId = 1;
        Pageable pageable = PageRequest.of(0, 1);

        GateInGeneralRegisterV2Input.Input input = GateInGeneralRegisterV2Input.Input.builder()
                .subBusinessUnitLocalId(subBusinessUnitLocalId)
                .equipmentCatEmptyFullId(catalogIds.get(CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS))
                .equipment("[]")
                .moveTypeId(catalogIds.get(CATALOG_CUSTOMER_RETURN))
                .chassis("{\"chassis_id\":1,\"chassis_number\":\"CHS1234567\"}")
                .driverId(driverId)
                .vehicleId(vehicleId)
                .truckCompanyId(truckCompanyId)
                .comments("")
                .userRegistrationId(userRegistrationId)
                .languageId(languageId)
                .operationCode("DEVCLNTE")
                .inputChassisNumber("CHS1234567")
                .flagChassisStayed(false)
                .inspectionSitu(true)
                .twrNumber("")
                .systemRuleId("sd1_rule_integration_sdy")
                .build();

        GateInGeneralRegisterV2Input.Root request = GateInGeneralRegisterV2Input.Root.builder()
                .prefix(GateInGeneralRegisterV2Input.Prefix.builder()
                        .input(input)
                        .build())
                .build();

        Eir dummyEir = Eir.builder()
                .id(1)
                .businessUnit(BusinessUnit.builder().id(businessUnitId).build())
                .subBusinessUnit(BusinessUnit.builder().id(subBusinessUnitId).build())
                .localSubBusinessUnit(BusinessUnit.builder().id(input.getSubBusinessUnitLocalId()).build())
                .catMovement(Catalog.builder().id(catalogIds.get(CATALOG_TYPE_GATE_IS_GATEIN_ALIAS)).build())
                .catEmptyFull(Catalog.builder().id(input.getEquipmentCatEmptyFullId()).build())
                .catOrigin(Optional.ofNullable(input.getMoveTypeId()).map(mt -> Catalog.builder().id(mt).build()).orElse(null))
                .truckArrivalDate(LocalDateTime.now())
                .container(null)
                .transportCompany(Company.builder().id(input.getTruckCompanyId()).build())
                .truck(Truck.builder().id(input.getVehicleId()).build())
                .driverPerson(Person.builder().id(input.getDriverId()).build())
                .shippingLine(null)
                .seal1("")
                .seal2("")
                .seal3("")
                .seal4("")
                .taraCnt(1)
                .cargoMaximumCnt(1)
                .isoCode(null)
                .catContainerType(null)
                .catSizeCnt(null)
                .catClassCnt(Catalog.builder().id(catalogIds.get(CATALOG_NO_GRADE)).build())
                .catTypeReefer(null)
                .catEngineBrand(null)
                .dateManufacture(LocalDateTime.now())
                .observation(input.getComments())
                .structureWithDamage(false)
                .structureDamaged(false)
                .machineryWithDamage(false)
                .machineryDamaged(false)
                .controlRevision(null)
                .dateRevision(LocalDateTime.now())
                .controlAssignment(null)
                .dateGateOutInspection(LocalDateTime.now())
                .truckMultipleLoad(false)
                .truckRetreatWithLoad(false)
                .active(true)
                .catCreationOrigin(Catalog.builder().id(catalogIds.get(CATALOG_GI_GENERAL_CREATION)).build())
                .registrationUser(User.builder().id(input.getUserRegistrationId()).build())
                .registrationDate(LocalDateTime.now())
                .clientCompany(null)
                .traceEir("ins_gi_general")
                .controlAssignmentLight(null)
                .weightGoods(null)
                .catMeasureWeight(null)
                .catMeansTransport(Catalog.builder().id(catalogIds.get(CATALOG_MEANS_TRANSPORT)).build())
                .catMeasureTare(null)
                .catMeasurePayload(null)
                .transportPlanningDetailFull(null)
                .returnDateH(null)
                .externalDocumentNumber("")
                .chassisNumber(input.getInputChassisNumber())
                .flagChassisStayed(input.getFlagChassisStayed())
                .flagOnSiteInspection(input.getInspectionSitu())
                .flagNoMaersk(true)
                .numberTwr(input.getTwrNumber())
                .build();

        when(eirRepository.save(any())).thenReturn(dummyEir);

        // WHEN
        GateInGeneralRegisterV2Output.Output output = gateInGeneralRegisterV2Service.registerGateInTransactional(request);

        // THEN
        assertNotNull(output);
        assertNotNull(output.getResult());

    }

}