package com.maersk.sd1.sdg.controller;

import com.maersk.sd1.common.Constants;
import com.maersk.sd1.sdg.controller.dto.BookingPreAllocationContainerListInput;
import com.maersk.sd1.sdg.controller.dto.BookingPreAllocationContainerListOutput;
import com.maersk.sd1.sdg.controller.dto.BookingResponse;
import com.maersk.sd1.sdg.dto.TotalOutputDTO;
import com.maersk.sd1.sdg.service.BookingPreAllocationContainerListService;
import com.maersk.sd1.common.controller.dto.ResponseController;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.http.ResponseEntity;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

class BookingPreAllocationContainerListControllerTest {

    @Mock
    private BookingPreAllocationContainerListService service;

    @InjectMocks
    private BookingPreAllocationContainerListController controller;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testBookingPreAllocationContainerList_ValidRequest() {
        // Arrange
        BookingPreAllocationContainerListInput.Root request = new BookingPreAllocationContainerListInput.Root();
        BookingPreAllocationContainerListInput.Prefix prefix = new BookingPreAllocationContainerListInput.Prefix();
        BookingPreAllocationContainerListInput.Input input = new BookingPreAllocationContainerListInput.Input();
        prefix.setInput(input);
        request.setPrefix(prefix);

        List<BookingPreAllocationContainerListOutput> data = new ArrayList<>();
        TotalOutputDTO total = new TotalOutputDTO();
        BookingResponse bookingResponse = new BookingResponse(data, List.of(total));
        ResponseController<BookingResponse> responseController = new ResponseController<>(bookingResponse);
        when(service.bookingPreAllocationContainerListService(any())).thenReturn(ResponseEntity.ok(responseController));

        // Act
        ResponseEntity<ResponseController<BookingResponse>> response = controller.bookingPreAllocationContainerList(request);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(responseController, response.getBody());
    }

    @Test
    void testBookingPreAllocationContainerList_InvalidRequest() {
        // Act
        ResponseEntity<ResponseController<BookingResponse>> response = controller.bookingPreAllocationContainerList(null);

        // Assert
        assertEquals(200, response.getStatusCode().value());
        assertEquals(Constants.INVALID_INPUT, response.getBody().getMessage());
    }

    @Test
    void testBookingPreAllocationContainerList_Exception() {
        // Arrange
        BookingPreAllocationContainerListInput.Root request = new BookingPreAllocationContainerListInput.Root();
        BookingPreAllocationContainerListInput.Prefix prefix = new BookingPreAllocationContainerListInput.Prefix();
        BookingPreAllocationContainerListInput.Input input = new BookingPreAllocationContainerListInput.Input();
        prefix.setInput(input);
        request.setPrefix(prefix);

        when(service.bookingPreAllocationContainerListService(any())).thenThrow(new RuntimeException("Test Exception"));

        // Act
        ResponseEntity<ResponseController<BookingResponse>> response = controller.bookingPreAllocationContainerList(request);

        // Assert
        assertEquals(500, response.getStatusCode().value());
        assertEquals("An error occurred while processing the request.", response.getBody().getMessage());
    }
}