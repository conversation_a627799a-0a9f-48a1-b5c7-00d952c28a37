package com.maersk.sd1.sdg.service;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdg.controller.dto.BookingPreAllocationContainerListInput;
import com.maersk.sd1.sdg.controller.dto.BookingPreAllocationContainerListOutput;
import com.maersk.sd1.sdg.controller.dto.BookingResponse;
import com.maersk.sd1.sdg.dto.*;
import com.maersk.sd1.common.dto.CatalogFindDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;

import java.lang.System;
import java.time.LocalDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BookingPreAllocationContainerListServiceTest {

    @Mock
    private BusinessUnitRepository businessUnitRepository;
    @Mock
    private CatalogRepository catalogRepository;
    @Mock
    private CargoDocumentRepository cargoDocumentRepository;
    @Mock
    private StockEmptyRepository stockEmptyRepository;
    @Mock
    private EirRepository eirRepository;
    @Mock
    private ContainerRepository containerRepository;
    @Mock
    private FgisInspectionRepository fgisInspectionRepository;
    @Mock
    private EirActivityZoneRepository eirActivityZoneRepository;
    @Mock
    private ContainerPreassignmentRepository containerPreassignmentRepository;
    @Mock
    private StockFullRepository stockFullRepository;
    @Mock
    private ContainerRestrictionRepository containerRestrictionRepository;
    @Mock
    private CatalogLanguageRepository catalogLanguageRepository;
    @Mock
    private CargoDocumentDetailRepository cargoDocumentDetailRepository;
    @Mock
    private BookingRepository bookingRepository;
    @Mock
    private BookingDetailRepository bookingDetailRepository;
    @Mock
    private EirDocumentCargoDetailRepository eirDocumentCargoDetailRepository;
    @Mock
    private CompanyRepository companyRepository;
    @Mock
    private ProductRepository productRepository;
    @Mock
    private IsoCodeRepository isoCodeRepository;

    @InjectMocks
    private BookingPreAllocationContainerListService service;

    private BookingPreAllocationContainerListInput.Root input;

    @BeforeEach
    public void setUp() {
        BookingPreAllocationContainerListInput.Input inputData = new BookingPreAllocationContainerListInput.Input();
        inputData.setSubBusinessUnitLocalId(1);
        inputData.setLanguageId(1);
        inputData.setContainerSizeId(1);
        inputData.setContainerTypeId(1);
        inputData.setDocumentoCargaId(1);
        inputData.setContainerGradeId(1);
        inputData.setContainerShippingLineId(1);
        inputData.setEmptyFullId(1);

        BookingPreAllocationContainerListInput.Prefix prefix = new BookingPreAllocationContainerListInput.Prefix();
        prefix.setInput(inputData);

        input = new BookingPreAllocationContainerListInput.Root();
        input.setPrefix(prefix);
    }

    @Test
    void testBookingPreAllocationContainerListService() {
        // Mock business unit repository
        lenient().when(businessUnitRepository.findParentBusinessUnitId(anyInt())).thenReturn(1);

        // Mock catalog repository to return the required catalog values using correct Parameter constants
        List<CatalogFindDTO> catalogResults = Arrays.asList(
            new CatalogFindDTO(1, "43081"),  // CATALOG_TYPE_GATE_IS_GATEOUT_ALIAS -> isGateOut
            new CatalogFindDTO(2, "43084"),  // CATALOG_TYPE_PROCESS_IS_FULL_ALIAS -> isFull
            new CatalogFindDTO(3, "43083"),  // CATALOG_TYPE_PROCESS_IS_EMPTY_ALIAS -> isEmptyy
            new CatalogFindDTO(4, "sd1_equipment_category_chassis"), // EQUIPMENT_CATEGORY_CHASSIS -> isChassis
            new CatalogFindDTO(5, "sd1_equipment_category_container"), // EQUIPMENT_CATEGORY_CONTAINER -> isContainer
            new CatalogFindDTO(6, "cat_43161_box_inspection") // CATALOG_BOX_INSPECTION_ALIAS -> isMtyActivityInsp
        );
        lenient().when(catalogRepository.findIdsByAliasesIn(anyList())).thenReturn(catalogResults);

        // Mock other repositories
        lenient().when(cargoDocumentRepository.findcargoDocumentBycargoIdId(anyInt())).thenReturn("Test");
        lenient().when(stockEmptyRepository.getTbDataList(anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(new ArrayList<>());
        lenient().when(eirActivityZoneRepository.findMtyStructureInspection(anyInt())).thenReturn(new ArrayList<>());
        lenient().when(fgisInspectionRepository.fetchFgInspectionData()).thenReturn(new ArrayList<>());
        lenient().when(stockFullRepository.updateEquipmentDetails(anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt())).thenReturn(new ArrayList<>());
        lenient().when(containerRestrictionRepository.getRestriccionContenedores()).thenReturn(new ArrayList<>());

        ResponseEntity<ResponseController<BookingResponse>> response = service.bookingPreAllocationContainerListService(input);

        assertEquals(200, response.getStatusCode().value());
    }
    @Test
    void testGetTbRestrictionBookingPreAllocations() {
        List<TbRestriction> mockData = new ArrayList<>();
        TbRestriction mockRestriction = mock(TbRestriction.class);
        when(mockRestriction.getCatEmptyFullId()).thenReturn(1);
        when(mockRestriction.getContainerId()).thenReturn(1);
        when(mockRestriction.getRestrictionRemark()).thenReturn("remark");
        when(mockRestriction.getNumeroBooking()).thenReturn("booking");
        mockData.add(mockRestriction);
        when(containerRestrictionRepository.getRestriccionContenedores()).thenReturn(mockData);

        List<TbRestrictionBookingPreAllocation> result = service.getTbRestrictionBookingPreAllocations();

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getCatEmptyFullId());
        assertEquals(1, result.get(0).getContainerId());
        assertEquals("remark", result.get(0).getRestrictionRemark());
        assertEquals("booking", result.get(0).getNumeroBooking());
    }

    @Test
    void testGetFgInspectionData() {
        List<tbFgisBooking> mockData = new ArrayList<>();
        tbFgisBooking mockBooking = mock(tbFgisBooking.class);
        when(mockBooking.getFgisDate()).thenReturn(new Date());
        when(mockBooking.getGateInEirId()).thenReturn(1);
        when(mockBooking.getContainerId()).thenReturn(100);
        mockData.add(mockBooking);
        when(fgisInspectionRepository.fetchFgInspectionData()).thenReturn(mockData);

        List<TbDataStockEmpty> tbDataList = new ArrayList<>();
        TbDataStockEmpty mockTbData = mock(TbDataStockEmpty.class);
        when(mockTbData.getCatEmptyFullId()).thenReturn(1);
        when(mockTbData.getCatEquipmentCategory()).thenReturn(1);
        when(mockTbData.getEirId()).thenReturn(1);
        tbDataList.add(mockTbData);

        List<tbFgisBookingPreAllocation> result = service.getFgInspectionData(tbDataList, 1, 1);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getGateInEirId());
        assertEquals(100, result.get(0).getContainerId());
    }

    @Test
    void testUpdateUsdaApprovalStatus() {
        List<TbDataStockEmpty> tbDataList = new ArrayList<>();
        TbDataStockEmpty tbData = new TbDataStockEmpty();
        tbData.setCatEmptyFullId(1);
        tbData.setCatEquipmentCategory(1);
        tbData.setContainerId(1);
        tbDataList.add(tbData);

        List<tbFgisBookingPreAllocation> tbFGIS = new ArrayList<>();
        tbFgisBookingPreAllocation tbFgis = new tbFgisBookingPreAllocation(new Date(), 1, 1);
        tbFGIS.add(tbFgis);

        FgisInspection fgisInspection = new FgisInspection();
        fgisInspection.setUsdaApproved(true);
        Container container = new Container();
        container.setId(1);
        fgisInspection.setContainer(container);

        Eir eir = new Eir();
        eir.setId(1);
        fgisInspection.setGateInEir(eir);

        Set<LocalDateTime> fgisDates = new HashSet<>();
        fgisDates.add(LocalDateTime.now());

        when(fgisInspectionRepository.findByGateInEirIdInAndFgisDateIn(anySet(), anySet())).thenReturn(Collections.singletonList(fgisInspection));

        service.updateUsdaApprovalStatus(tbDataList, tbFGIS, 1, 1);

        assertNull(tbDataList.get(0).getUsdaApproved());
    }

    @Test
    void testCalculateDwellTime() {
        Date gateInDate = new Date(System.currentTimeMillis() - 86400000L);
        String dwellTime = service.calculateDwellTime(gateInDate);
        assertEquals("2", dwellTime);
    }

    @Test
    void testGetGateInDate() {
        Date date = new Date();
        String formattedDate = service.getGateInDate(date);
        assertNotNull(formattedDate);
        assertFalse(formattedDate.isEmpty());
    }

    @Test
    void testGetUsdaApproved() {
        assertEquals("Yes", service.getUsdaApproved(1));
        assertEquals("No", service.getUsdaApproved(2));
        assertEquals("", service.getUsdaApproved(0));
    }

    @Test
    void testGetEquipmentRestriction() {
        // Arrange
        Integer tb = 1;
        Integer catEquip = 1;
        Integer isContainer = 1;
        Integer isEmptyy = 1;
        Integer isFull = 2;

        List<TbRestrictionBookingPreAllocation> restrictions = new ArrayList<>();
        TbRestrictionBookingPreAllocation restriction1 = new TbRestrictionBookingPreAllocation();
        restriction1.setContainerId(1);
        restriction1.setCatEmptyFullId(1);
        restriction1.setRestrictionRemark("Empty Restriction");
        restriction1.setNumeroBooking("Booking1");

        TbRestrictionBookingPreAllocation restriction2 = new TbRestrictionBookingPreAllocation();
        restriction2.setContainerId(1);
        restriction2.setCatEmptyFullId(2);
        restriction2.setRestrictionRemark("Full Restriction");
        restriction2.setNumeroBooking("Booking2");

        restrictions.add(restriction1);
        restrictions.add(restriction2);

        // Act
        String result = service.getEquipmentRestriction(tb, catEquip, restrictions, isContainer, isEmptyy, isFull);

        // Assert
        assertEquals("[Empty]Empty Restriction: Booking1 [Full]Full Restriction: Booking2", result);
    }

    @Test
    void testCountEmptyFullRestrictions() {
        // Arrange
        List<TbDataStockEmpty> tbDataList = new ArrayList<>();
        TbDataStockEmpty tbData1 = new TbDataStockEmpty();
        tbData1.setContainerId(1);
        tbData1.setCatEquipmentCategory(1);
        tbDataList.add(tbData1);

        TbDataStockEmpty tbData2 = new TbDataStockEmpty();
        tbData2.setContainerId(2);
        tbData2.setCatEquipmentCategory(1);
        tbDataList.add(tbData2);

        List<TbRestrictionBookingPreAllocation> tbRestrictionList = new ArrayList<>();
        TbRestrictionBookingPreAllocation restriction1 = new TbRestrictionBookingPreAllocation();
        restriction1.setContainerId(1);
        restriction1.setCatEmptyFullId(1);
        restriction1.setRestrictionRemark("");
        tbRestrictionList.add(restriction1);

        TbRestrictionBookingPreAllocation restriction2 = new TbRestrictionBookingPreAllocation();
        restriction2.setContainerId(2);
        restriction2.setCatEmptyFullId(2);
        restriction2.setRestrictionRemark("");
        tbRestrictionList.add(restriction2);

        Integer isContainer = 1;
        Integer isEmptyy = 1;
        Integer isFull = 2;

        // Act
        int result = service.countEmptyFullRestrictions(tbDataList, tbRestrictionList, isContainer, isEmptyy, isFull);

        // Assert
        assertEquals(2, result);
    }

    @Test
    void testProcessTbDataList() {
        // Arrange
        List<TbDataStockEmpty> dataList = new ArrayList<>();
        TbDataStockEmpty data1 = new TbDataStockEmpty();
        data1.setContainerId(1);
        data1.setCatEquipmentCategory(1);
        data1.setCatEmptyFullId(1);
        data1.setEquipmentSizeId(1);
        data1.setEquipmentTypeId(1);
        data1.setCodigoIsoId(1);
        data1.setShippingLineId(1);
        data1.setCatStructureConditionId(1);
        data1.setCatMachineryConditionId(1);
        data1.setCatStructureConditionInspId(1);
        data1.setCatMachineryConditionInspId(1);
        data1.setProductoId(1);
        data1.setLocal("Depot1");
        data1.setEquipmentNumber("EQ123");
        data1.setCatClaseId(1);
        data1.setFechaIngresoCamion(new Date());
        data1.setEirId(1);
        data1.setGinComment("Gin Comment");
        data1.setInspectorComment("Inspector Comment");
        data1.setBookingPreAllocation("Booking1");
        data1.setPotencialFoodAid(1);
        data1.setUsdaApproved(1);
        data1.setShipperName("Shipper1");
        dataList.add(data1);

        List<TbRestrictionBookingPreAllocation> restrictionList = new ArrayList<>();
        TbRestrictionBookingPreAllocation restriction1 = new TbRestrictionBookingPreAllocation();
        restriction1.setContainerId(1);
        restriction1.setCatEmptyFullId(1);
        restriction1.setRestrictionRemark("");
        restriction1.setNumeroBooking("");
        restrictionList.add(restriction1);

        Integer languageId = 1;
        Integer isContainer = 1;
        Integer isChassis = 2;
        Integer isEmptyy = 1;
        Integer isFull = 2;

        // Mocking repository methods
        lenient().when(catalogLanguageRepository.fnCatalogTranslationDescLong(anyInt(), eq(languageId))).thenReturn("Translated");
        lenient().when(isoCodeRepository.findCodeByIsoCodeId(anyInt())).thenReturn("ISO123");
        lenient().when(productRepository.findProductName(anyInt())).thenReturn("Product1");
        lenient().when(catalogRepository.findDescriptionById(anyInt())).thenReturn("Grade1");

        // Act
        List<BookingPreAllocationContainerListOutput> result = service.processTbDataList(dataList, languageId, isContainer, isChassis, isEmptyy, isFull, restrictionList);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        BookingPreAllocationContainerListOutput output = result.get(0);
        assertEquals("Depot1", output.getDepot());
        assertNotNull(output.getEmptyFull());
        assertEquals("EQ123", output.getEquipmentNumber());
        assertEquals("", output.getEquipmentSizeType());
        assertEquals("Grade1", output.getEquipmentGrade());
        assertEquals("ISO123", output.getIsoCode());
        assertEquals("Translated", output.getShippingLine());
        assertEquals("Shipper1", output.getShipperName());
        assertEquals("Product1", output.getCommodity());
        assertNotNull(output.getGateInDate());
        assertNotNull(output.getDwellTime());
        assertEquals("Translated", output.getStructureConditionCurrent());
        assertEquals("Translated", output.getMachineryConditionCurrent());
        assertEquals(1, output.getEquipmentSizeId());
        assertEquals(1, output.getEquipmentTypeId());
        assertEquals(1, output.getContainerId());
        assertEquals(1, output.getTotal());
    }

    @Test
    void testUpdateShipperDetails() {
        // Arrange
        List<TbDataStockEmpty> tbDataList = new ArrayList<>();
        TbDataStockEmpty data1 = new TbDataStockEmpty();
        data1.setEirId(1);
        tbDataList.add(data1);

        EirDocumentCargoDetail eirDoc = new EirDocumentCargoDetail();
        eirDoc.setCargoDocumentDetail(new CargoDocumentDetail());
        lenient().when(eirDocumentCargoDetailRepository.findOneByEirId(1)).thenReturn(eirDoc);

        CargoDocumentDetail dodx = new CargoDocumentDetail();
        dodx.setCargoDocument(new CargoDocument());
        dodx.setProduct(new Product());
        lenient().when(cargoDocumentDetailRepository.findByCargoDetailId(anyInt())).thenReturn(dodx);

        CargoDocument doc = new CargoDocument();
        doc.setShipperCompany(new Company());
        lenient().when(cargoDocumentRepository.findcargoDoc(anyInt())).thenReturn(doc);

        Company shipper = new Company();
        shipper.setDocument("12345");
        shipper.setLegalName("ShipperName");
        lenient().when(companyRepository.findByCompanyId(anyInt())).thenReturn(shipper);

        // Act
        List<TbDataStockEmpty> result = service.updateShipperDetails(tbDataList);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        TbDataStockEmpty updatedData = result.get(0);
        assertNull(updatedData.getShipperNro());
        assertNull(updatedData.getShipperName());
    }
}