package com.maersk.sd1.seg.service;

import com.maersk.sd1.seg.controller.dto.UserPasswordChangeInput;
import com.maersk.sd1.seg.controller.dto.UserPasswordChangeOutput;
import com.maersk.sd1.common.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserPasswordChangeServiceTest {

    @Mock
    private UserRepository userRepository;

    @InjectMocks
    private UserPasswordChangeService service;

    private UserPasswordChangeInput.Input input;

    @BeforeEach
    void setUp() {
        input = new UserPasswordChangeInput.Input();
        input.setUserId(123);
        input.setKey("yIR1dcttkFWAjEmGg75Jbw==");
        input.setOrigin("IND");
    }

    @Test
    void givenValidInput_WhenChangePasswordForIND_ThenReturnsSuccess() {
        when(userRepository.updatePasswordForINDCase(any(Integer.class), any(String.class), any(LocalDateTime.class), any(Integer.class), any(LocalDateTime.class)))
                .thenReturn(1);

        UserPasswordChangeOutput output = service.changePassword(input);
        assertEquals(1, output.getRespStatus());
        assertEquals("Contraseña cambiada correctamente", output.getRespMessage());
    }

    @Test
    void givenDatabaseError_WhenChangePasswordForIND_ThenReturnsError() {
        when(userRepository.updatePasswordForINDCase(any(Integer.class), any(String.class), any(LocalDateTime.class), any(Integer.class), any(LocalDateTime.class)))
                .thenThrow(new RuntimeException("Database error"));

        UserPasswordChangeOutput output = service.changePassword(input);
        assertEquals(0, output.getRespStatus());
        assertEquals("Database error", output.getRespMessage());
    }

    @Test
    void givenNonINDOrigin_WhenChangePassword_ThenNoUpdate() {
        input.setOrigin("ARG");
        UserPasswordChangeOutput output = service.changePassword(input);
        assertEquals(1, output.getRespStatus());
        assertEquals("Contraseña cambiada correctamente", output.getRespMessage());
    }
}
