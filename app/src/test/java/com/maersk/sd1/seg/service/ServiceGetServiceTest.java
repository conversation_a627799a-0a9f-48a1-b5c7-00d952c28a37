package com.maersk.sd1.seg.service;

import com.maersk.sd1.seg.controller.dto.ServiceGetOutput;
import com.maersk.sd1.seg.dto.ServiceGetServiceDto;
import com.maersk.sd1.seg.dto.ServiceGetRoleDto;
import com.maersk.sd1.common.repository.ServiceRepository;
import com.maersk.sd1.common.repository.RoleServiceRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class ServiceGetServiceTest {

    @Mock
    private ServiceRepository serviceRepository;

    @Mock
    private RoleServiceRepository roleServiceRepository;

    @InjectMocks
    private ServiceGetService serviceGetService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void given_ValidServiceId_When_GetServicioData_Then_ReturnsData() {

        Integer servicioId = 123;
        ServiceGetServiceDto serviceDto = new ServiceGetServiceDto(
                123,
                "Test Service",
                'Y',
                true,
                999,
                LocalDateTime.now().minusDays(1),
                1000,
                LocalDateTime.now()
        );
        List<ServiceGetRoleDto> roles = Arrays.asList(
                new ServiceGetRoleDto(1, 2,"Role1"),
                new ServiceGetRoleDto(2, 2, "Role2")
        );

        when(serviceRepository.findServicioById(servicioId)).thenReturn(Optional.of(serviceDto));
        when(roleServiceRepository.findRolesByServiceId(servicioId)).thenReturn(roles);

        ServiceGetOutput result = serviceGetService.getServicioData(servicioId);

        verify(serviceRepository, times(1)).findServicioById(servicioId);
        verify(roleServiceRepository, times(1)).findRolesByServiceId(servicioId);
        assertNotNull(result);
        assertEquals(1, result.getRespStatus());
        assertEquals("Success", result.getRespMessage());
        assertNotNull(result.getService());
        assertEquals(2, result.getListRoles().size());
    }

    @Test
    void given_NonExistentServiceId_When_GetServicioData_Then_ReturnsError() {

        Integer servicioId = 999;

        when(serviceRepository.findServicioById(servicioId)).thenReturn(Optional.empty());

        ServiceGetOutput result = serviceGetService.getServicioData(servicioId);

        verify(serviceRepository, times(1)).findServicioById(servicioId);
        verify(roleServiceRepository, never()).findRolesByServiceId(servicioId);
        assertNotNull(result);
        assertEquals(0, result.getRespStatus());
        assertTrue(result.getRespMessage().contains("Service not found"));
    }
}