package com.maersk.sd1.sde.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sde.dto.BookingBlockCancellationRegisterInput;
import com.maersk.sd1.sde.dto.BookingBlockCancellationRegisterOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BookingBlockCancellationRegisterServiceTest {

    @Mock
    private BookingRepository bookingRepository;

    @Mock
    private CargoDocumentRepository cargoDocumentRepository;

    @Mock
    private BookingBlockCancellationRepository bookingBlockCancellationRepository;

    @Mock
    private BookingBlockCancellationDetailRepository bookingBlockCancellationDetailRepository;

    @Mock
    private CargoDocumentDetailRepository cargoDocumentDetailRepository;

    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private BookingBlockCancellationRegisterService bookingBlockCancellationRegisterService;

    private BookingBlockCancellationRegisterInput.Input input;

    @BeforeEach
    public void setUp() {
        input = new BookingBlockCancellationRegisterInput.Input();
        input.setBusinessUnitId(1);
        input.setSubBusinessUnitId(1);
        input.setCatType(1);
        input.setCatReason(1);
        input.setComment("Test comment");
        input.setUserRegistrationId(1);
        input.setDocumentCargoId(1);
        input.setLanguageId(1);
    }

    @Test
    void testRegisterCancelBookingBlockSuccess() {
        Booking booking = new Booking();
        booking.setId(1);
        booking.setBookingNumber("BK123");

        CargoDocument cargoDocument = new CargoDocument();
        cargoDocument.setId(1);

        when(cargoDocumentRepository.findBookingIdByCargoDocumentId(anyInt())).thenReturn(1);
        when(bookingRepository.findActiveBookingById(anyInt())).thenReturn(Optional.of(booking));
        when(bookingBlockCancellationRepository.findDuplicateCount(anyInt(), anyInt(), anyInt())).thenReturn(0);
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Success");
        when(cargoDocumentRepository.findActiveCargoDocById(anyInt())).thenReturn(Optional.of(cargoDocument));

        BookingBlockCancellationRegisterOutput output = bookingBlockCancellationRegisterService.registerCancelBookingBlock(input);

        assertEquals(1, output.getRespEstado());
        assertEquals("Success", output.getRespMensaje());
    }

    @Test
    void testRegisterCancelBookingBlockBookingNotFound() {
        when(cargoDocumentRepository.findBookingIdByCargoDocumentId(anyInt())).thenReturn(null);
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Booking does not exist or is inactive.");

        BookingBlockCancellationRegisterOutput output = bookingBlockCancellationRegisterService.registerCancelBookingBlock(input);

        assertEquals(0, output.getRespEstado());
        assertEquals("Booking does not exist or is inactive.", output.getRespMensaje());
    }

    @Test
    void testRegisterCancelBookingBlockBookingInactive() {
        // Test when booking ID is found but the booking is inactive or not found
        when(cargoDocumentRepository.findBookingIdByCargoDocumentId(anyInt())).thenReturn(1);
        when(bookingRepository.findActiveBookingById(anyInt())).thenReturn(Optional.empty());
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Booking does not exist or is inactive.");

        BookingBlockCancellationRegisterOutput output = bookingBlockCancellationRegisterService.registerCancelBookingBlock(input);

        assertEquals(0, output.getRespEstado());
        assertEquals("Booking does not exist or is inactive.", output.getRespMensaje());
    }

    @Test
    void testRegisterCancelBookingBlockDuplicateCancellation() {
        Booking booking = new Booking();
        booking.setId(1);

        when(cargoDocumentRepository.findBookingIdByCargoDocumentId(anyInt())).thenReturn(1);
        when(bookingRepository.findActiveBookingById(anyInt())).thenReturn(Optional.of(booking));
        when(bookingBlockCancellationRepository.findDuplicateCount(anyInt(), anyInt(), anyInt())).thenReturn(1);
        when(messageLanguageRepository.fnTranslatedMessage(anyString(), anyInt(), anyInt())).thenReturn("Booking already has a cancellation/block for this deposit.");

        BookingBlockCancellationRegisterOutput output = bookingBlockCancellationRegisterService.registerCancelBookingBlock(input);

        assertEquals(0, output.getRespEstado());
        assertEquals("Booking already has a cancellation/block for this deposit.", output.getRespMensaje());
    }

    @Test
    void testRegisterCancelBookingBlockException() {
        when(cargoDocumentRepository.findBookingIdByCargoDocumentId(anyInt())).thenThrow(new RuntimeException("Internal Server Error"));

        BookingBlockCancellationRegisterOutput output = bookingBlockCancellationRegisterService.registerCancelBookingBlock(input);

        assertEquals(0, output.getRespEstado());
        assertEquals("Internal Server Error", output.getRespMensaje());
    }
}