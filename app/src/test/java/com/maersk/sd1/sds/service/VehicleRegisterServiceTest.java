// File: VehicleRegisterServiceTest.java
package com.maersk.sd1.sds.service;

import com.maersk.sd1.common.model.Company;
import com.maersk.sd1.common.model.Truck;
import com.maersk.sd1.common.repository.CompanyRepository;
import com.maersk.sd1.common.repository.MessageLanguageRepository;
import com.maersk.sd1.common.repository.TruckRepository;
import com.maersk.sd1.sds.dto.VehicleRegisterOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Optional;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class VehicleRegisterServiceTest {

    @Mock
    private TruckRepository truckRepository;
    @Mock
    private CompanyRepository companyRepository;
    @Mock
    private MessageLanguageRepository messageLanguageRepository;

    @InjectMocks
    private VehicleRegisterService vehicleRegisterService;

    private final String plate = "ABC123";
    private final BigDecimal payload = BigDecimal.valueOf(1500.50);
    private final String model = "Model X";
    private final BigDecimal netWeight = BigDecimal.valueOf(1200.75);
    private final BigDecimal bruteWeight = BigDecimal.valueOf(2000.00);
    private final Integer transportCompanyId = 101;
    private final String companyDocument = "DOC123456";
    private final Boolean active = true;
    private final Integer userRegistrationId = 501;
    private final Integer languageId = 1;

    @BeforeEach
    void setUp() {
        lenient().when(messageLanguageRepository.fnTranslatedMessage(ArgumentMatchers.eq("GENERAL"), ArgumentMatchers.eq(9), ArgumentMatchers.anyInt()))
                .thenReturn("Success message");
        lenient().when(messageLanguageRepository.fnTranslatedMessage(ArgumentMatchers.eq("INS_VEHICULO"), ArgumentMatchers.eq(1), ArgumentMatchers.anyInt()))
                .thenReturn("Truck already exists: {IDX}");
    }

    @Test
    void shouldReturnSuccessForNewTruckRegistration() {
        Company company = new Company();
        company.setId(201);
        when(companyRepository.findByDocumentIgnoreCase(companyDocument))
                .thenReturn(Optional.of(company));
        when(truckRepository.findByPlateIgnoreCase(plate))
                .thenReturn(Optional.empty());
        Truck savedTruck = new Truck();
        savedTruck.setId(1001);
        savedTruck.setRegistrationDate(LocalDateTime.now());
        when(truckRepository.saveAndFlush(ArgumentMatchers.any(Truck.class)))
                .thenReturn(savedTruck);
        VehicleRegisterOutput output = vehicleRegisterService.registerVehicle(plate, payload, model, netWeight,
                bruteWeight, transportCompanyId, companyDocument, active, userRegistrationId, languageId);
        assertEquals(1, output.getRespEstado());
        assertEquals(1001, output.getRespNewId());
        assertEquals("Success message", output.getRespMensaje());
    }

    @Test
    void shouldReturnDuplicateTruckWhenTruckExists() {
        Truck existingTruck = new Truck();
        existingTruck.setId(2002);
        when(truckRepository.findByPlateIgnoreCase(plate))
                .thenReturn(Optional.of(existingTruck));
        VehicleRegisterOutput output = vehicleRegisterService.registerVehicle(plate, payload, model, netWeight,
                bruteWeight, transportCompanyId, companyDocument, active, userRegistrationId, languageId);
        assertEquals(2, output.getRespEstado());
        assertEquals(0, output.getRespNewId());
        String expectedMessage = "Truck already exists: " + existingTruck.getId();
        assertEquals(expectedMessage, output.getRespMensaje());
    }

    @Test
    void shouldReturnSuccessUsingFallbackCompanyWhenNoCompanyFoundInInput() {
        when(companyRepository.findByDocumentIgnoreCase(companyDocument))
                .thenReturn(Optional.empty());
        when(companyRepository.findById(transportCompanyId))
                .thenReturn(Optional.empty());
        Company fallbackCompany = new Company();
        fallbackCompany.setId(999);
        when(companyRepository.findByDocumentIgnoreCase("ETVARIOS"))
                .thenReturn(Optional.of(fallbackCompany));
        when(truckRepository.findByPlateIgnoreCase(plate))
                .thenReturn(Optional.empty());
        Truck savedTruck = new Truck();
        savedTruck.setId(3003);
        when(truckRepository.saveAndFlush(ArgumentMatchers.any(Truck.class)))
                .thenReturn(savedTruck);
        VehicleRegisterOutput output = vehicleRegisterService.registerVehicle(plate, payload, model, netWeight,
                bruteWeight, transportCompanyId, companyDocument, active, userRegistrationId, languageId);
        assertEquals(1, output.getRespEstado());
        assertEquals(3003, output.getRespNewId());
        assertEquals("Success message", output.getRespMensaje());
    }

    @Test
    void shouldReturnErrorWhenExceptionOccursDuringRegistration() {
        when(companyRepository.findByDocumentIgnoreCase(companyDocument))
                .thenReturn(Optional.empty());
        when(companyRepository.findById(transportCompanyId))
                .thenReturn(Optional.empty());
        when(companyRepository.findByDocumentIgnoreCase("ETVARIOS"))
                .thenReturn(Optional.empty());
        when(truckRepository.findByPlateIgnoreCase(plate))
                .thenReturn(Optional.empty());
        doThrow(new RuntimeException("DB error")).when(truckRepository).saveAndFlush(ArgumentMatchers.any(Truck.class));
        VehicleRegisterOutput output = vehicleRegisterService.registerVehicle(plate, payload, model, netWeight,
                bruteWeight, transportCompanyId, companyDocument, active, userRegistrationId, languageId);
        assertEquals(0, output.getRespEstado());
        assertEquals(0, output.getRespNewId());
        assertEquals("DB error", output.getRespMensaje());
    }
}